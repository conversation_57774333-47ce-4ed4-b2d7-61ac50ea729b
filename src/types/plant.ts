
export interface Plant {
  id: string;
  name: string;
  location: string;
  type: "Manufacturing" | "Assembly" | "Processing" | "Distribution";
  status: "Active" | "Inactive" | "Maintenance" | "Planned";
  capacity: number;
  utilization: number;
  employees: number;
  products: string[];
  region: string;
  manager: string;
  establishedDate: string;
  area: number; // in sq ft
  certifications: string[];
  energyRating: "A" | "B" | "C" | "D";
}

export interface PlantPerformance {
  plantId: string;
  efficiency: number;
  qualityScore: number;
  safetyScore: number;
  onTimeDelivery: number;
  costPerUnit: number;
  maintenanceHours: number;
  downtime: number;
}

export interface PlantMetrics {
  totalPlants: number;
  activeBlants: number;
  totalCapacity: number;
  averageUtilization: number;
  totalEmployees: number;
  maintenancePlants: number;
}
