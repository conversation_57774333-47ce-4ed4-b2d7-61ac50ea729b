
export interface Constraint {
  id: number;
  name: string;
  type: string;
  category: string;
  status: string;
  priority: string;
  description: string;
  lastModified: string;
  appliedTo: string;
}

export interface Violation {
  id: number;
  constraintName: string;
  violationType: string;
  severity: string;
  driver: string;
  vehicle: string;
  route: string;
  timestamp: string;
  duration: string;
  status: string;
  description: string;
}

export interface ComplianceMetric {
  category: string;
  totalChecks: number;
  violations: number;
  complianceRate: number;
  trend: "up" | "down" | "stable";
}
