
export interface SKU {
  id: string;
  name: string;
  category: string;
  description?: string;
  price: number;
  cost: number;
  margin: number;
  isPerishable: boolean;
  prepTime: number; // in minutes
  shelfLife?: number; // in hours
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  status: 'active' | 'inactive' | 'draft' | 'low_stock' | 'out_of_stock';
  tags: string[];
  createdAt: string;
  updatedAt: string;
  stockLevel?: number;
  minStockLevel?: number;
  maxStockLevel?: number;
  popularity?: 'high' | 'medium' | 'low';
}

export interface PricingRule {
  id: string;
  name: string;
  type: 'discount' | 'surge' | 'bundle' | 'buy-x-get-y';
  conditions: {
    timeOfDay?: string[];
    dayOfWeek?: string[];
    minOrderValue?: number;
    skuIds?: string[];
    customerSegment?: string;
  };
  action: {
    discountPercent?: number;
    discountAmount?: number;
    surgeMultiplier?: number;
    bundleSKUs?: string[];
    freeItemId?: string;
  };
  isActive: boolean;
  validFrom: string;
  validTo: string;
  priority: number;
}

export interface Zone {
  id: string;
  name: string;
  type: 'delivery' | 'pickup' | 'hybrid';
  coordinates: {
    lat: number;
    lng: number;
  }[];
  maxDeliveryTime: number;
  minOrderValue: number;
  deliveryFee: number;
  isActive: boolean;
  capacityLimit: number;
  currentOrders: number;
}

export interface DemandForecast {
  skuId: string;
  skuName: string;
  predictedDemand: number;
  confidence: number;
  period: 'hourly' | 'daily' | 'weekly';
  factors: string[];
  recommendations: string[];
}

export interface CommerceInsight {
  id: string;
  type: 'restock' | 'pricing' | 'zone-expansion' | 'menu-optimization' | 'demand-surge';
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  impact: string;
  confidence: number;
  action: string;
  data?: any;
}

export interface JnaOrder {
  id: string;
  customerId: string;
  status: 'pending' | 'confirmed' | 'preparing' | 'dispatched' | 'delivered' | 'cancelled';
  items: {
    skuId: string;
    quantity: number;
    price: number;
  }[];
  totalAmount: number;
  deliveryAddress: string;
  zoneId: string;
  estimatedDeliveryTime: string;
  actualDeliveryTime?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ONDCCallback {
  id: string;
  type: 'on_search' | 'on_select' | 'on_init' | 'on_confirm' | 'on_track' | 'on_cancel' | 'on_update' | 'on_status';
  orderId: string;
  payload: any;
  timestamp: string;
  processed: boolean;
  error?: string;
}
