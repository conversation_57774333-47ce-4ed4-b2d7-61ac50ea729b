
export interface LaneConstraintMapping {
  id: number;
  laneName: string;
  constraintName: string;
  constraintType: string;
  value: string;
  isActive: boolean;
  overrideValue: string | null;
  lastModified: string;
}

export interface OverrideRule {
  id: number;
  ruleName: string;
  description: string;
  conditions: string;
  overrideType: string;
  targetValue: string;
  priority: number;
  isActive: boolean;
  appliedLanes: string[];
  createdBy: string;
  createdDate: string;
  lastModified: string;
}

export interface ConstraintTemplate {
  id: number;
  templateName: string;
  description: string;
  category: string;
  constraints: string[];
  defaultValues: Record<string, string>;
  applicableLaneTypes: string[];
  isDefault: boolean;
  usageCount: number;
  createdBy: string;
  createdDate: string;
  lastModified: string;
}
