
export interface Warehouse {
  id: string;
  name: string;
  location: string;
  type: "Distribution" | "Storage" | "Cross-Dock" | "Fulfillment";
  status: "Active" | "Inactive" | "Maintenance" | "Planned";
  capacity: number;
  utilization: number;
  employees: number;
  zones: string[];
  region: string;
  manager: string;
  establishedDate: string;
  area: number; // in sq ft
  automationLevel: "Manual" | "Semi-Automated" | "Fully-Automated";
  temperatureControlled: boolean;
}

export interface WarehousePerformance {
  warehouseId: string;
  efficiency: number;
  accuracy: number;
  throughput: number;
  orderFulfillment: number;
  inventoryTurnover: number;
  pickingSpeed: number;
  errorRate: number;
}

export interface WarehouseMetrics {
  totalWarehouses: number;
  activeWarehouses: number;
  totalCapacity: number;
  averageUtilization: number;
  totalEmployees: number;
  maintenanceWarehouses: number;
}
