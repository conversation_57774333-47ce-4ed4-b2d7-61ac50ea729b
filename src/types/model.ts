
export interface ModelData {
  id: string;
  name: string;
  type: 'network-flow' | 'inventory' | 'routing' | 'facility-location' | 'scheduling';
  status: 'draft' | 'active' | 'archived' | 'deprecated';
  version: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  description?: string;
  tags?: string[];
  lastRunAt?: string;
  accuracy?: number;
  isTemplate?: boolean;
  parentModelId?: string;
  solver?: 'gurobi' | 'cplex' | 'or-tools';
  objectiveFunction?: 'minimize-cost' | 'maximize-service' | 'balance';
}

export interface ModelLifecycleAction {
  type: 'create' | 'edit' | 'duplicate' | 'archive' | 'restore' | 'delete' | 'promote' | 'deprecate';
  label: string;
  icon: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary';
  requiresConfirmation?: boolean;
}

export interface BulkOperation {
  type: 'archive' | 'delete' | 'change-status' | 'add-tags';
  label: string;
  icon: string;
  requiresConfirmation: boolean;
}
