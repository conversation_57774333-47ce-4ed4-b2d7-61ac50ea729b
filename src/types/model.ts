
export interface ModelData {
  id: string;
  name: string;
  type: 'optimization' | 'analysis' | 'prediction';
  status: 'draft' | 'active' | 'archived' | 'deprecated';
  version: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  description?: string;
  tags?: string[];
  lastRunAt?: string;
  accuracy?: number;
  isTemplate?: boolean;
  parentModelId?: string;
}

export interface ModelLifecycleAction {
  type: 'create' | 'edit' | 'duplicate' | 'archive' | 'restore' | 'delete' | 'promote' | 'deprecate';
  label: string;
  icon: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary';
  requiresConfirmation?: boolean;
}

export interface BulkOperation {
  type: 'archive' | 'delete' | 'change-status' | 'add-tags';
  label: string;
  icon: string;
  requiresConfirmation: boolean;
}
