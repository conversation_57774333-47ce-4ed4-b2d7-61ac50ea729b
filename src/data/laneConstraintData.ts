
import { LaneConstraintMapping, OverrideRule, ConstraintTemplate } from "@/types/laneConstraint";

export const laneConstraints: LaneConstraintMapping[] = [
  {
    id: 1,
    laneName: "Chicago-Detroit Express",
    constraintName: "Weight Limit - Interstate",
    constraintType: "Capacity",
    value: "80,000 lbs",
    isActive: true,
    overrideValue: null,
    lastModified: "2024-05-15"
  },
  {
    id: 2,
    laneName: "Chicago-Detroit Express",
    constraintName: "Driver Hours - DOT Regulation",
    constraintType: "Time",
    value: "11 hours max",
    isActive: true,
    overrideValue: null,
    lastModified: "2024-05-10"
  },
  {
    id: 3,
    laneName: "West Coast Corridor",
    constraintName: "Hazmat Route Restriction",
    constraintType: "Route",
    value: "No tunnels/bridges",
    isActive: true,
    overrideValue: null,
    lastModified: "2024-05-20"
  },
  {
    id: 4,
    laneName: "Northeast Route",
    constraintName: "Delivery Window - Downtown",
    constraintType: "Time",
    value: "7AM - 11AM only",
    isActive: false,
    overrideValue: "6AM - 12PM",
    lastModified: "2024-05-18"
  },
  {
    id: 5,
    laneName: "Texas Triangle",
    constraintName: "Vehicle Height Restriction",
    constraintType: "Physical",
    value: "13.6 ft max",
    isActive: true,
    overrideValue: null,
    lastModified: "2024-05-12"
  }
];

export const overrideRules: OverrideRule[] = [
  {
    id: 1,
    ruleName: "Emergency Weather Override",
    description: "Override weight limits during severe weather conditions",
    conditions: "Weather severity >= 7 AND visibility < 0.5 miles",
    overrideType: "Value Override",
    targetValue: "70,000 lbs",
    priority: 9,
    isActive: true,
    appliedLanes: ["Chicago-Detroit Express", "West Coast Corridor"],
    createdBy: "John Smith",
    createdDate: "2024-04-15",
    lastModified: "2024-05-20"
  },
  {
    id: 2,
    ruleName: "Holiday Delivery Extension",
    description: "Extend delivery windows during holiday seasons",
    conditions: "Date in holiday_periods AND delivery_type = 'express'",
    overrideType: "Time Override",
    targetValue: "5AM - 2PM",
    priority: 6,
    isActive: true,
    appliedLanes: ["Northeast Route", "Texas Triangle"],
    createdBy: "Maria Garcia",
    createdDate: "2024-03-10",
    lastModified: "2024-05-18"
  },
  {
    id: 3,
    ruleName: "Construction Zone Detour",
    description: "Temporary route modifications for construction",
    conditions: "Construction_active = true AND lane_type = 'interstate'",
    overrideType: "Condition Override",
    targetValue: "Use alternate route config",
    priority: 8,
    isActive: false,
    appliedLanes: ["Chicago-Detroit Express"],
    createdBy: "David Wilson",
    createdDate: "2024-02-20",
    lastModified: "2024-05-15"
  }
];

export const constraintTemplates: ConstraintTemplate[] = [
  {
    id: 1,
    templateName: "Interstate Standard",
    description: "Standard constraints for interstate transportation lanes",
    category: "Interstate",
    constraints: ["Weight Limit - Interstate", "Driver Hours - DOT Regulation", "Speed Limit - Highway"],
    defaultValues: {
      "Weight Limit - Interstate": "80,000 lbs",
      "Driver Hours - DOT Regulation": "11 hours max",
      "Speed Limit - Highway": "65 mph"
    },
    applicableLaneTypes: ["Interstate", "Highway"],
    isDefault: true,
    usageCount: 12,
    createdBy: "System",
    createdDate: "2024-01-01",
    lastModified: "2024-05-10"
  },
  {
    id: 2,
    templateName: "Urban Delivery",
    description: "Optimized constraints for urban delivery routes",
    category: "Urban",
    constraints: ["Delivery Window - Downtown", "Vehicle Height Restriction", "Noise Restriction"],
    defaultValues: {
      "Delivery Window - Downtown": "7AM - 11AM only",
      "Vehicle Height Restriction": "13.6 ft max",
      "Noise Restriction": "< 85 dB"
    },
    applicableLaneTypes: ["Urban", "City", "Downtown"],
    isDefault: false,
    usageCount: 8,
    createdBy: "Jane Doe",
    createdDate: "2024-02-15",
    lastModified: "2024-05-18"
  },
  {
    id: 3,
    templateName: "Hazmat Express",
    description: "Specialized constraints for hazardous materials transport",
    category: "Hazmat",
    constraints: ["Hazmat Route Restriction", "Special Permits Required", "Emergency Response Plan"],
    defaultValues: {
      "Hazmat Route Restriction": "No tunnels/bridges",
      "Special Permits Required": "Class A, B permits",
      "Emergency Response Plan": "Required"
    },
    applicableLaneTypes: ["Hazmat", "Chemical", "Dangerous Goods"],
    isDefault: false,
    usageCount: 5,
    createdBy: "Safety Team",
    createdDate: "2024-03-01",
    lastModified: "2024-05-20"
  }
];
