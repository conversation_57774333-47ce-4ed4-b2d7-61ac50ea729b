
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";

export interface Supplier {
  id: string;
  name: string;
  category: string;
  region: string;
  status: "Active" | "Inactive" | "Pending";
  contactEmail: string;
  contactPhone: string;
  address: string;
  rating: number;
  certifications: string[];
}

interface SupplierDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  supplier?: Supplier;
  onSave: (supplier: Supplier) => void;
  mode: "create" | "edit";
}

const SupplierDialog = ({ open, onOpenChange, supplier, onSave, mode }: SupplierDialogProps) => {
  const [formData, setFormData] = useState<Partial<Supplier>>({
    name: "",
    category: "",
    region: "",
    status: "Active",
    contactEmail: "",
    contactPhone: "",
    address: "",
    rating: 0,
    certifications: []
  });
  const [newCertification, setNewCertification] = useState("");

  useEffect(() => {
    if (supplier && mode === "edit") {
      setFormData(supplier);
    } else {
      setFormData({
        name: "",
        category: "",
        region: "",
        status: "Active",
        contactEmail: "",
        contactPhone: "",
        address: "",
        rating: 0,
        certifications: []
      });
    }
  }, [supplier, mode, open]);

  const handleSave = () => {
    const supplierData: Supplier = {
      id: supplier?.id || `S${Date.now()}`,
      name: formData.name || "",
      category: formData.category || "",
      region: formData.region || "",
      status: formData.status || "Active",
      contactEmail: formData.contactEmail || "",
      contactPhone: formData.contactPhone || "",
      address: formData.address || "",
      rating: formData.rating || 0,
      certifications: formData.certifications || []
    };
    onSave(supplierData);
    onOpenChange(false);
  };

  const addCertification = () => {
    if (newCertification.trim() && !formData.certifications?.includes(newCertification.trim())) {
      setFormData(prev => ({
        ...prev,
        certifications: [...(prev.certifications || []), newCertification.trim()]
      }));
      setNewCertification("");
    }
  };

  const removeCertification = (cert: string) => {
    setFormData(prev => ({
      ...prev,
      certifications: prev.certifications?.filter(c => c !== cert) || []
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{mode === "create" ? "Add New Supplier" : "Edit Supplier"}</DialogTitle>
          <DialogDescription>
            {mode === "create" ? "Create a new supplier record" : "Update supplier information"}
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Supplier Name</Label>
            <Input
              id="name"
              value={formData.name || ""}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter supplier name"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select value={formData.category || ""} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Raw Materials">Raw Materials</SelectItem>
                <SelectItem value="Components">Components</SelectItem>
                <SelectItem value="Electronics">Electronics</SelectItem>
                <SelectItem value="Packaging">Packaging</SelectItem>
                <SelectItem value="Services">Services</SelectItem>
                <SelectItem value="Logistics">Logistics</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="region">Region</Label>
            <Select value={formData.region || ""} onValueChange={(value) => setFormData(prev => ({ ...prev, region: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select region" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="North America">North America</SelectItem>
                <SelectItem value="Europe">Europe</SelectItem>
                <SelectItem value="Asia Pacific">Asia Pacific</SelectItem>
                <SelectItem value="Latin America">Latin America</SelectItem>
                <SelectItem value="Africa">Africa</SelectItem>
                <SelectItem value="Middle East">Middle East</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select value={formData.status || "Active"} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value as Supplier["status"] }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Inactive">Inactive</SelectItem>
                <SelectItem value="Pending">Pending</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="contactEmail">Contact Email</Label>
            <Input
              id="contactEmail"
              type="email"
              value={formData.contactEmail || ""}
              onChange={(e) => setFormData(prev => ({ ...prev, contactEmail: e.target.value }))}
              placeholder="<EMAIL>"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="contactPhone">Contact Phone</Label>
            <Input
              id="contactPhone"
              value={formData.contactPhone || ""}
              onChange={(e) => setFormData(prev => ({ ...prev, contactPhone: e.target.value }))}
              placeholder="+****************"
            />
          </div>
          
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="address">Address</Label>
            <Input
              id="address"
              value={formData.address || ""}
              onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
              placeholder="Enter supplier address"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="rating">Rating (0-5)</Label>
            <Input
              id="rating"
              type="number"
              min="0"
              max="5"
              step="0.1"
              value={formData.rating || 0}
              onChange={(e) => setFormData(prev => ({ ...prev, rating: parseFloat(e.target.value) || 0 }))}
            />
          </div>
          
          <div className="space-y-2">
            <Label>Certifications</Label>
            <div className="flex gap-2">
              <Input
                value={newCertification}
                onChange={(e) => setNewCertification(e.target.value)}
                placeholder="Add certification"
                onKeyPress={(e) => e.key === "Enter" && addCertification()}
              />
              <Button type="button" onClick={addCertification}>Add</Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.certifications?.map((cert, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {cert}
                  <X className="h-3 w-3 cursor-pointer" onClick={() => removeCertification(cert)} />
                </Badge>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            {mode === "create" ? "Create Supplier" : "Update Supplier"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SupplierDialog;
