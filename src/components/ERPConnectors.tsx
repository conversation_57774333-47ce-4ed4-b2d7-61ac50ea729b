
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Database, 
  Plug, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  Settings,
  ExternalLink,
  Wifi,
  WifiOff
} from "lucide-react";

interface ERPSystem {
  id: string;
  name: string;
  type: string;
  status: "connected" | "disconnected" | "syncing" | "error";
  lastSync: string;
  logo: string;
  dataPoints: string[];
}

const ERPConnectors = () => {
  const [systems, setSystems] = useState<ERPSystem[]>([
    {
      id: "sap",
      name: "SAP ERP",
      type: "Enterprise Resource Planning",
      status: "connected",
      lastSync: "2 minutes ago",
      logo: "S",
      dataPoints: ["Inventory", "Orders", "Suppliers", "Financial Data"]
    },
    {
      id: "oracle",
      name: "Oracle NetSuite",
      type: "Cloud ERP",
      status: "connected",
      lastSync: "5 minutes ago",
      logo: "O",
      dataPoints: ["Customer Data", "Sales Orders", "Procurement"]
    },
    {
      id: "microsoft",
      name: "Microsoft Dynamics",
      type: "Business Applications",
      status: "syncing",
      lastSync: "Syncing now...",
      logo: "M",
      dataPoints: ["Manufacturing", "Supply Chain", "Finance"]
    },
    {
      id: "quickbooks",
      name: "QuickBooks Enterprise",
      type: "Accounting Software",
      status: "error",
      lastSync: "1 hour ago",
      logo: "Q",
      dataPoints: ["Accounting", "Payroll", "Reporting"]
    }
  ]);

  const [newConnection, setNewConnection] = useState("");

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "connected": return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "syncing": return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />;
      case "error": return <AlertCircle className="h-4 w-4 text-red-600" />;
      default: return <WifiOff className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "connected": return "bg-green-100 text-green-800";
      case "syncing": return "bg-blue-100 text-blue-800";
      case "error": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const handleConnect = (systemId: string) => {
    setSystems(prev => prev.map(system => 
      system.id === systemId 
        ? { ...system, status: "syncing" as const, lastSync: "Connecting..." }
        : system
    ));

    // Simulate connection process
    setTimeout(() => {
      setSystems(prev => prev.map(system => 
        system.id === systemId 
          ? { ...system, status: "connected" as const, lastSync: "Just now" }
          : system
      ));
    }, 3000);
  };

  const addNewConnection = () => {
    if (!newConnection.trim()) return;
    
    const newSystem: ERPSystem = {
      id: newConnection.toLowerCase().replace(/\s+/g, '-'),
      name: newConnection,
      type: "Custom Integration",
      status: "disconnected",
      lastSync: "Never",
      logo: newConnection.charAt(0).toUpperCase(),
      dataPoints: ["Custom Data"]
    };
    
    setSystems(prev => [...prev, newSystem]);
    setNewConnection("");
  };

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Database className="h-5 w-5 text-blue-600" />
          ERP System Connectors
        </CardTitle>
        <Button variant="outline" size="sm">
          <Settings className="h-4 w-4 mr-1" />
          Configure
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Add New Connection */}
        <div className="p-3 border rounded-lg bg-gray-50">
          <div className="flex gap-2">
            <Input
              placeholder="Enter ERP system name..."
              value={newConnection}
              onChange={(e) => setNewConnection(e.target.value)}
              className="flex-1"
            />
            <Button onClick={addNewConnection} size="sm">
              <Plug className="h-4 w-4 mr-1" />
              Add
            </Button>
          </div>
        </div>

        {/* Connected Systems */}
        {systems.map((system) => (
          <div key={system.id} className="p-4 border rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                  {system.logo}
                </div>
                <div>
                  <h4 className="font-medium">{system.name}</h4>
                  <p className="text-sm text-gray-500">{system.type}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIcon(system.status)}
                <Badge variant="outline" className={getStatusColor(system.status)}>
                  {system.status}
                </Badge>
              </div>
            </div>

            <div className="flex flex-wrap gap-1 mb-3">
              {system.dataPoints.map((point) => (
                <Badge key={point} variant="secondary" className="text-xs">
                  {point}
                </Badge>
              ))}
            </div>

            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>Last sync: {system.lastSync}</span>
              <div className="flex gap-2">
                {system.status === "disconnected" || system.status === "error" ? (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleConnect(system.id)}
                  >
                    <Wifi className="h-3 w-3 mr-1" />
                    Connect
                  </Button>
                ) : (
                  <Button variant="outline" size="sm">
                    <ExternalLink className="h-3 w-3 mr-1" />
                    View Data
                  </Button>
                )}
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default ERPConnectors;
