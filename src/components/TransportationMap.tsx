import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Maximize2, 
  Minimize2, 
  Layers, 
  Filter, 
  MapPin, 
  Truck, 
  Warehouse, 
  Settings,
  BarChart3,
  Database,
  Map as MapIcon,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Download,
  Upload,
  RefreshCw,
  Play,
  Pause,
  Plus,
  Copy,
  Trash2,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Package,
  Clock,
  Users,
  Globe,
  PieChart,
  LineChart,
  Activity,
  Brain,
  Target,
  Zap,
  Sliders,
  ShoppingCart,
  Factory
} from "lucide-react";

const TransportationMap = () => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showLegend, setShowLegend] = useState(true);
  const [currentScenario, setCurrentScenario] = useState("triad-greenfield");
  const [simulationRunning, setSimulationRunning] = useState(true);
  const [layers, setLayers] = useState({
    serviceBand: true,
    selectedFacilities: true,
    closedFacilities: false,
    demandZones: true,
    stateBorders: true
  });
  const [demandFilter, setDemandFilter] = useState({
    high: true,
    medium: true,
    low: true
  });

  // Sample scenarios data
  const scenarios = [
    {
      id: "triad-greenfield",
      name: "Triad (Greenfield)",
      type: "Greenfield",
      status: "running",
      totalCost: "$2.4M",
      serviceLevel: "94.2%",
      facilities: 12,
      lastModified: "2 hours ago",
      description: "Optimized greenfield scenario for Southeast expansion"
    },
    {
      id: "current-state",
      name: "Current State Analysis",
      type: "Baseline",
      status: "completed",
      totalCost: "$3.1M",
      serviceLevel: "89.5%",
      facilities: 8,
      lastModified: "1 day ago",
      description: "Current network configuration baseline"
    },
    {
      id: "cost-optimized",
      name: "Cost Optimized",
      type: "Optimization",
      status: "draft",
      totalCost: "$2.1M",
      serviceLevel: "91.8%",
      facilities: 10,
      lastModified: "3 days ago",
      description: "Minimized total cost while maintaining service levels"
    }
  ];

  // Sample data tables
  const facilitiesData = [
    { id: 1, name: "Atlanta DC", type: "Distribution Center", status: "Active", capacity: "500K sq ft", utilization: "87%", cost: "$450K" },
    { id: 2, name: "Savannah Port", type: "Port Facility", status: "Active", capacity: "200K sq ft", utilization: "95%", cost: "$320K" },
    { id: 3, name: "Charlotte Hub", type: "Hub", status: "Active", capacity: "300K sq ft", utilization: "78%", cost: "$280K" },
    { id: 4, name: "Augusta Facility", type: "Warehouse", status: "Closed", capacity: "150K sq ft", utilization: "0%", cost: "$0" }
  ];

  const demandData = [
    { zone: "Atlanta Metro", demand: "12.5K units", risk: "High", coverage: "98%", cost: "$125K" },
    { zone: "Charlotte Region", demand: "8.2K units", risk: "Medium", coverage: "94%", cost: "$89K" },
    { zone: "Savannah Area", demand: "6.1K units", risk: "Low", coverage: "96%", cost: "$67K" },
    { zone: "Augusta Zone", demand: "4.8K units", risk: "Medium", coverage: "92%", cost: "$54K" }
  ];

  // Sample inventory data for the optimization module
  const inventoryData = [
    { sku: "SKU-001", product: "Widget A", stock: 2450, safetyStock: 500, reorderPoint: 800, turnover: 8.2 },
    { sku: "SKU-002", product: "Widget B", stock: 1200, safetyStock: 300, reorderPoint: 450, turnover: 12.1 },
    { sku: "SKU-003", product: "Widget C", stock: 850, safetyStock: 200, reorderPoint: 350, turnover: 6.7 },
    { sku: "SKU-004", product: "Widget D", stock: 3200, safetyStock: 800, reorderPoint: 1200, turnover: 15.3 }
  ];

  const policyData = [
    { policy: "Reorder Policy", current: "Min-Max", recommended: "Economic Order Quantity", impact: "+12% efficiency" },
    { policy: "Safety Stock", current: "Fixed", recommended: "Dynamic", impact: "-8% carrying cost" },
    { policy: "Lead Time", current: "Static", recommended: "Probabilistic", impact: "+15% service level" },
    { policy: "ABC Classification", current: "Annual Value", recommended: "Service Level", impact: "+5% turnover" }
  ];

  // US States outline (simplified representation)
  const stateData = [
    { name: "Georgia", path: "M 60 45 L 80 45 L 80 65 L 60 65 Z" },
    { name: "North Carolina", path: "M 60 30 L 85 30 L 85 45 L 60 45 Z" },
    { name: "South Carolina", path: "M 65 45 L 80 45 L 80 55 L 65 55 Z" },
    { name: "Florida", path: "M 70 65 L 85 65 L 85 85 L 70 85 Z" },
    { name: "Tennessee", path: "M 50 25 L 80 25 L 80 35 L 50 35 Z" },
  ];

  // Demand zones with risk levels
  const demandZones = [
    { id: 1, name: "Atlanta Metro", risk: "high", x: "62%", y: "47%", radius: 25 },
    { id: 2, name: "Charlotte Region", risk: "medium", x: "67%", y: "37%", radius: 20 },
    { id: 3, name: "Savannah Area", risk: "low", x: "77%", y: "67%", radius: 15 },
    { id: 4, name: "Augusta Zone", risk: "medium", x: "72%", y: "52%", radius: 18 },
    { id: 5, name: "Jacksonville", risk: "high", x: "75%", y: "75%", radius: 22 },
  ];

  // Facilities data
  const facilities = [
    { id: 1, name: "Atlanta DC", type: "selected", x: "60%", y: "45%", status: "active" },
    { id: 2, name: "Savannah Port", type: "selected", x: "75%", y: "65%", status: "active" },
    { id: 3, name: "Charlotte Hub", type: "selected", x: "65%", y: "35%", status: "active" },
    { id: 4, name: "Augusta Facility", type: "closed", x: "70%", y: "50%", status: "closed" },
    { id: 5, name: "Jacksonville Port", type: "selected", x: "75%", y: "75%", status: "active" },
  ];

  const toggleLayer = (layer: string) => {
    setLayers(prev => ({ ...prev, [layer]: !prev[layer] }));
  };

  const toggleDemandFilter = (risk: string) => {
    setDemandFilter(prev => ({ ...prev, [risk]: !prev[risk] }));
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'rgba(220, 38, 38, 0.3)';
      case 'medium': return 'rgba(245, 158, 11, 0.3)';
      case 'low': return 'rgba(16, 185, 129, 0.3)';
      default: return 'rgba(156, 163, 175, 0.3)';
    }
  };

  const getRiskBorderColor = (risk: string) => {
    switch (risk) {
      case 'high': return '#dc2626';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#9ca3af';
    }
  };

  const currentScenarioData = scenarios.find(s => s.id === currentScenario);

  return (
    <div className="space-y-4">
      {/* Dashboard Header */}
      <div className="flex items-center justify-between bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <MapIcon className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-semibold">Supply Chain Scenario Dashboard</h2>
          </div>
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            {currentScenarioData?.name || "Triad (Greenfield)"}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <div className={`w-2 h-2 rounded-full ${simulationRunning ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
            {simulationRunning ? 'Live Simulation' : 'Simulation Paused'}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSimulationRunning(!simulationRunning)}
          >
            {simulationRunning ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
          </Button>
        </div>
      </div>

      {/* Dashboard Tabs */}
      <Tabs defaultValue="maps" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="maps" className="flex items-center gap-2">
            <MapIcon className="h-4 w-4" />
            Maps
          </TabsTrigger>
          <TabsTrigger value="data" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Data
          </TabsTrigger>
          <TabsTrigger value="scenarios" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Scenarios
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="optimization" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Optimization
          </TabsTrigger>
        </TabsList>

        <TabsContent value="maps" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* Layer Controls */}
            <div className="lg:col-span-1 space-y-4">
              {/* Layer Toggle Card */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Layers className="h-4 w-4" />
                    Map Layers
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="service-band" className="text-sm">Service Band</Label>
                    <Switch 
                      id="service-band"
                      checked={layers.serviceBand}
                      onCheckedChange={() => toggleLayer('serviceBand')}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="selected-facilities" className="text-sm">Selected Facilities</Label>
                    <Switch 
                      id="selected-facilities"
                      checked={layers.selectedFacilities}
                      onCheckedChange={() => toggleLayer('selectedFacilities')}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="closed-facilities" className="text-sm">Closed Facilities</Label>
                    <Switch 
                      id="closed-facilities"
                      checked={layers.closedFacilities}
                      onCheckedChange={() => toggleLayer('closedFacilities')}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="demand-zones" className="text-sm">Demand Zones</Label>
                    <Switch 
                      id="demand-zones"
                      checked={layers.demandZones}
                      onCheckedChange={() => toggleLayer('demandZones')}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="state-borders" className="text-sm">State Borders</Label>
                    <Switch 
                      id="state-borders"
                      checked={layers.stateBorders}
                      onCheckedChange={() => toggleLayer('stateBorders')}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Demand Risk Filters */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    Demand Risk Zones
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <Label htmlFor="high-risk" className="text-sm">High Risk</Label>
                    </div>
                    <Switch 
                      id="high-risk"
                      checked={demandFilter.high}
                      onCheckedChange={() => toggleDemandFilter('high')}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <Label htmlFor="medium-risk" className="text-sm">Medium Risk</Label>
                    </div>
                    <Switch 
                      id="medium-risk"
                      checked={demandFilter.medium}
                      onCheckedChange={() => toggleDemandFilter('medium')}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <Label htmlFor="low-risk" className="text-sm">Low Risk</Label>
                    </div>
                    <Switch 
                      id="low-risk"
                      checked={demandFilter.low}
                      onCheckedChange={() => toggleDemandFilter('low')}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Map Visualization */}
            <div className="lg:col-span-3">
              <Card className="relative h-[600px] bg-gradient-to-br from-blue-50 to-green-50 border-0 shadow-lg overflow-hidden">
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <MapIcon className="h-5 w-5" />
                      United States - Transportation Network
                    </span>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        Southeast Region
                      </Badge>
                    </div>
                  </CardTitle>
                </CardHeader>
                
                <CardContent className="relative h-full p-0">
                  {/* Map Background */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-100 via-green-50 to-blue-200">
                    <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
                      {/* State Borders */}
                      {layers.stateBorders && stateData.map((state, index) => (
                        <path
                          key={index}
                          d={state.path}
                          fill="rgba(59, 130, 246, 0.1)"
                          stroke="#3b82f6"
                          strokeWidth="0.2"
                          className="hover:fill-blue-200 transition-colors cursor-pointer"
                        />
                      ))}

                      {/* Service Band (if enabled) */}
                      {layers.serviceBand && (
                        <circle
                          cx="65"
                          cy="50"
                          r="20"
                          fill="rgba(16, 185, 129, 0.1)"
                          stroke="#10b981"
                          strokeWidth="0.3"
                          strokeDasharray="2,1"
                        />
                      )}
                    </svg>

                    {/* Demand Zones */}
                    {layers.demandZones && demandZones.map((zone) => 
                      demandFilter[zone.risk] && (
                        <div
                          key={zone.id}
                          className="absolute transform -translate-x-1/2 -translate-y-1/2 rounded-full border-2 flex items-center justify-center cursor-pointer group"
                          style={{ 
                            left: zone.x, 
                            top: zone.y,
                            width: `${zone.radius}px`,
                            height: `${zone.radius}px`,
                            backgroundColor: getRiskColor(zone.risk),
                            borderColor: getRiskBorderColor(zone.risk)
                          }}
                        >
                          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-white/95 backdrop-blur-sm px-2 py-1 rounded text-xs font-medium shadow-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                            {zone.name} - {zone.risk} risk
                          </div>
                        </div>
                      )
                    )}

                    {/* Facilities */}
                    {facilities.map((facility) => {
                      const shouldShow = 
                        (facility.type === 'selected' && layers.selectedFacilities) ||
                        (facility.type === 'closed' && layers.closedFacilities);
                      
                      if (!shouldShow) return null;

                      return (
                        <div
                          key={facility.id}
                          className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
                          style={{ left: facility.x, top: facility.y }}
                        >
                          <div className={`w-8 h-8 rounded-full shadow-lg flex items-center justify-center transition-all group-hover:scale-110 ${
                            facility.type === 'selected' 
                              ? 'bg-blue-600' 
                              : 'bg-gray-500'
                          }`}>
                            {facility.status === 'active' ? (
                              <Warehouse className="h-4 w-4 text-white" />
                            ) : (
                              <XCircle className="h-4 w-4 text-white" />
                            )}
                          </div>
                          
                          <div className="absolute top-10 left-1/2 transform -translate-x-1/2 bg-white/95 backdrop-blur-sm px-2 py-1 rounded text-xs font-medium shadow-md opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                            {facility.name}
                            {facility.status === 'closed' && (
                              <span className="text-red-600 ml-1">(Closed)</span>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Map Controls */}
                  <div className="absolute top-4 right-4 flex flex-col gap-2">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => setIsFullscreen(!isFullscreen)}
                      className="bg-white/90 hover:bg-white shadow-md"
                    >
                      {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
                    </Button>
                    
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => setShowLegend(!showLegend)}
                      className="bg-white/90 hover:bg-white shadow-md"
                    >
                      <Layers className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Enhanced Legend */}
                  {showLegend && (
                    <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg p-4 shadow-lg max-w-xs">
                      <h4 className="text-sm font-semibold mb-3">Map Legend</h4>
                      <div className="space-y-3 text-xs">
                        <div>
                          <h5 className="font-medium mb-1">Facilities</h5>
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                                <Warehouse className="h-2 w-2 text-white" />
                              </div>
                              <span>Active Facilities</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 bg-gray-500 rounded-full flex items-center justify-center">
                                <XCircle className="h-2 w-2 text-white" />
                              </div>
                              <span>Closed Facilities</span>
                            </div>
                          </div>
                        </div>
                        
                        <div>
                          <h5 className="font-medium mb-1">Demand Risk Zones</h5>
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-2 bg-red-500/50 border border-red-500 rounded"></div>
                              <span>High Risk</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-2 bg-yellow-500/50 border border-yellow-500 rounded"></div>
                              <span>Medium Risk</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-2 bg-green-500/50 border border-green-500 rounded"></div>
                              <span>Low Risk</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h5 className="font-medium mb-1">Other</h5>
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-1 border border-blue-500 border-dashed rounded"></div>
                              <span>Service Band</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-1 border border-blue-600 rounded"></div>
                              <span>State Borders</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="data" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {/* Data Import/Export */}
            <div className="lg:col-span-1 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    Data Management
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full" size="sm">
                    <Upload className="h-3 w-3 mr-2" />
                    Import Data
                  </Button>
                  <Button variant="outline" className="w-full" size="sm">
                    <Download className="h-3 w-3 mr-2" />
                    Export Data
                  </Button>
                  <Button variant="outline" className="w-full" size="sm">
                    <RefreshCw className="h-3 w-3 mr-2" />
                    Refresh Data
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Data Sources</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Demand Forecast</span>
                    <Badge variant="secondary" className="text-xs">Connected</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Facility Costs</span>
                    <Badge variant="secondary" className="text-xs">Connected</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Transportation</span>
                    <Badge variant="outline" className="text-xs">Pending</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Data Tables */}
            <div className="lg:col-span-2 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Facilities Data</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">Facility</th>
                          <th className="text-left p-2">Type</th>
                          <th className="text-left p-2">Status</th>
                          <th className="text-left p-2">Capacity</th>
                          <th className="text-left p-2">Utilization</th>
                          <th className="text-left p-2">Cost</th>
                        </tr>
                      </thead>
                      <tbody>
                        {facilitiesData.map((facility) => (
                          <tr key={facility.id} className="border-b hover:bg-gray-50">
                            <td className="p-2 font-medium">{facility.name}</td>
                            <td className="p-2">{facility.type}</td>
                            <td className="p-2">
                              <Badge variant={facility.status === 'Active' ? 'secondary' : 'outline'} className="text-xs">
                                {facility.status}
                              </Badge>
                            </td>
                            <td className="p-2">{facility.capacity}</td>
                            <td className="p-2">{facility.utilization}</td>
                            <td className="p-2">{facility.cost}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Demand Zones Data</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">Zone</th>
                          <th className="text-left p-2">Demand</th>
                          <th className="text-left p-2">Risk Level</th>
                          <th className="text-left p-2">Coverage</th>
                          <th className="text-left p-2">Service Cost</th>
                        </tr>
                      </thead>
                      <tbody>
                        {demandData.map((zone, index) => (
                          <tr key={index} className="border-b hover:bg-gray-50">
                            <td className="p-2 font-medium">{zone.zone}</td>
                            <td className="p-2">{zone.demand}</td>
                            <td className="p-2">
                              <Badge 
                                variant={zone.risk === 'High' ? 'destructive' : zone.risk === 'Medium' ? 'outline' : 'secondary'} 
                                className="text-xs"
                              >
                                {zone.risk}
                              </Badge>
                            </td>
                            <td className="p-2">{zone.coverage}</td>
                            <td className="p-2">{zone.cost}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="scenarios" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* Scenario Controls */}
            <div className="lg:col-span-1 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Scenario Management
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full" size="sm">
                    <Plus className="h-3 w-3 mr-2" />
                    New Scenario
                  </Button>
                  <Button variant="outline" className="w-full" size="sm">
                    <Copy className="h-3 w-3 mr-2" />
                    Duplicate
                  </Button>
                  <Button variant="outline" className="w-full" size="sm">
                    <Download className="h-3 w-3 mr-2" />
                    Export
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Active Scenario</CardTitle>
                </CardHeader>
                <CardContent>
                  <Select value={currentScenario} onValueChange={setCurrentScenario}>
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {scenarios.map((scenario) => (
                        <SelectItem key={scenario.id} value={scenario.id}>
                          {scenario.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card>
            </div>

            {/* Scenarios List */}
            <div className="lg:col-span-3">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Available Scenarios</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {scenarios.map((scenario) => (
                      <div 
                        key={scenario.id}
                        className={`p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors ${
                          currentScenario === scenario.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                        }`}
                        onClick={() => setCurrentScenario(scenario.id)}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className="font-medium">{scenario.name}</h3>
                            <p className="text-sm text-gray-600 mt-1">{scenario.description}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {scenario.type}
                            </Badge>
                            <Badge 
                              variant={scenario.status === 'running' ? 'secondary' : scenario.status === 'completed' ? 'default' : 'outline'}
                              className="text-xs"
                            >
                              {scenario.status}
                            </Badge>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3">
                          <div>
                            <span className="text-xs text-gray-500">Total Cost</span>
                            <div className="font-semibold text-green-600">{scenario.totalCost}</div>
                          </div>
                          <div>
                            <span className="text-xs text-gray-500">Service Level</span>
                            <div className="font-semibold">{scenario.serviceLevel}</div>
                          </div>
                          <div>
                            <span className="text-xs text-gray-500">Facilities</span>
                            <div className="font-semibold">{scenario.facilities}</div>
                          </div>
                          <div>
                            <span className="text-xs text-gray-500">Modified</span>
                            <div className="text-sm text-gray-600">{scenario.lastModified}</div>
                          </div>
                        </div>

                        <div className="flex items-center justify-between mt-3 pt-3 border-t">
                          <div className="flex items-center gap-2">
                            {scenario.status === 'running' && (
                              <div className="flex items-center gap-1 text-xs text-green-600">
                                <Activity className="h-3 w-3" />
                                Running
                              </div>
                            )}
                          </div>
                          <div className="flex items-center gap-1">
                            <Button variant="ghost" size="sm">
                              <Copy className="h-3 w-3" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {/* KPI Cards */}
            <div className="lg:col-span-3 grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <DollarSign className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">Total Cost</span>
                  </div>
                  <div className="text-2xl font-bold text-green-600">$2.4M</div>
                  <div className="flex items-center gap-1 text-xs text-green-600 mt-1">
                    <TrendingDown className="h-3 w-3" />
                    -12% vs baseline
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">Service Level</span>
                  </div>
                  <div className="text-2xl font-bold">94.2%</div>
                  <div className="flex items-center gap-1 text-xs text-green-600 mt-1">
                    <TrendingUp className="h-3 w-3" />
                    +4.7% vs baseline
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Warehouse className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium">Facilities</span>
                  </div>
                  <div className="text-2xl font-bold">12</div>
                  <div className="flex items-center gap-1 text-xs text-green-600 mt-1">
                    <TrendingUp className="h-3 w-3" />
                    +4 facilities
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Globe className="h-4 w-4 text-orange-600" />
                    <span className="text-sm font-medium">Coverage</span>
                  </div>
                  <div className="text-2xl font-bold">89.7%</div>
                  <div className="flex items-center gap-1 text-xs text-gray-500 mt-1">
                    <Clock className="h-3 w-3" />
                    Real-time
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Charts Section */}
            <div className="lg:col-span-2 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <LineChart className="h-4 w-4" />
                    Cost Analysis Trends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Cost trends chart would be displayed here</p>
                      <p className="text-xs text-gray-400 mt-1">Interactive visualization with drill-down capabilities</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <PieChart className="h-4 w-4" />
                    Service Level Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-48 bg-gradient-to-br from-green-50 to-blue-50 rounded-lg flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <PieChart className="h-10 w-10 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Service level distribution chart</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Analytics Controls */}
            <div className="lg:col-span-1 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Analytics Controls
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label className="text-xs">Time Period</Label>
                    <Select defaultValue="current">
                      <SelectTrigger className="h-8 text-xs mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="current">Current Period</SelectItem>
                        <SelectItem value="q1">Q1 2024</SelectItem>
                        <SelectItem value="q2">Q2 2024</SelectItem>
                        <SelectItem value="annual">Annual View</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label className="text-xs">Metric Focus</Label>
                    <Select defaultValue="cost">
                      <SelectTrigger className="h-8 text-xs mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cost">Cost Analysis</SelectItem>
                        <SelectItem value="service">Service Level</SelectItem>
                        <SelectItem value="utilization">Utilization</SelectItem>
                        <SelectItem value="risk">Risk Assessment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button className="w-full" size="sm">
                    <RefreshCw className="h-3 w-3 mr-2" />
                    Refresh Analytics
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Key Insights</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center gap-2 mb-1">
                      <TrendingUp className="h-3 w-3 text-green-600" />
                      <span className="text-xs font-medium text-green-800">Cost Optimization</span>
                    </div>
                    <p className="text-xs text-green-700">23% cost reduction achieved vs baseline scenario</p>
                  </div>

                  <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center gap-2 mb-1">
                      <CheckCircle className="h-3 w-3 text-blue-600" />
                      <span className="text-xs font-medium text-blue-800">Service Improvement</span>
                    </div>
                    <p className="text-xs text-blue-700">Service level increased by 4.7 percentage points</p>
                  </div>

                  <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div className="flex items-center gap-2 mb-1">
                      <AlertTriangle className="h-3 w-3 text-yellow-600" />
                      <span className="text-xs font-medium text-yellow-800">Risk Alert</span>
                    </div>
                    <p className="text-xs text-yellow-700">High demand risk in Atlanta Metro area requires attention</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="optimization" className="space-y-4">
          {/* Supply Chain Optimization Header */}
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <Brain className="h-6 w-6 text-purple-600" />
                  Supply Chain Intelligence & Optimization
                </h3>
                <p className="text-gray-600 mt-1">End-to-end optimization across inventory, policies, and network design</p>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                  AI-Powered
                </Badge>
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  Multi-Objective
                </Badge>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Optimization Controls */}
            <div className="lg:col-span-1 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    Optimization Objectives
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-xs font-medium">Primary Objective</Label>
                    <Select defaultValue="cost-service">
                      <SelectTrigger className="h-8 text-xs mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cost-service">Cost vs Service Balance</SelectItem>
                        <SelectItem value="cost-min">Minimize Total Cost</SelectItem>
                        <SelectItem value="service-max">Maximize Service Level</SelectItem>
                        <SelectItem value="sustainability">Sustainability Focus</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label className="text-xs font-medium">Optimization Scope</Label>
                    <div className="space-y-2 mt-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="inventory-opt" className="text-xs">Inventory Levels</Label>
                        <Switch id="inventory-opt" defaultChecked />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="policy-opt" className="text-xs">Replenishment Policies</Label>
                        <Switch id="policy-opt" defaultChecked />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="network-opt" className="text-xs">Network Design</Label>
                        <Switch id="network-opt" defaultChecked />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="transport-opt" className="text-xs">Transportation</Label>
                        <Switch id="transport-opt" />
                      </div>
                    </div>
                  </div>

                  <Button className="w-full" size="sm">
                    <Zap className="h-3 w-3 mr-2" />
                    Run Optimization
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Sliders className="h-4 w-4" />
                    Optimization Status
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-xs">Last Run</span>
                    <span className="text-xs text-gray-600">2 hours ago</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs">Duration</span>
                    <span className="text-xs text-gray-600">12 minutes</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs">Status</span>
                    <Badge variant="secondary" className="text-xs">Completed</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs">Improvement</span>
                    <span className="text-xs text-green-600 font-medium">+18.5%</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main Content Area */}
            <div className="lg:col-span-3 space-y-4">
              {/* Optimization Results KPIs */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Package className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium">Inventory Value</span>
                    </div>
                    <div className="text-xl font-bold">$1.2M</div>
                    <div className="flex items-center gap-1 text-xs text-green-600 mt-1">
                      <TrendingDown className="h-3 w-3" />
                      -15% optimized
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Activity className="h-4 w-4 text-purple-600" />
                      <span className="text-sm font-medium">Turnover Rate</span>
                    </div>
                    <div className="text-xl font-bold">11.4x</div>
                    <div className="flex items-center gap-1 text-xs text-green-600 mt-1">
                      <TrendingUp className="h-3 w-3" />
                      +22% improved
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">Fill Rate</span>
                    </div>
                    <div className="text-xl font-bold">97.8%</div>
                    <div className="flex items-center gap-1 text-xs text-green-600 mt-1">
                      <TrendingUp className="h-3 w-3" />
                      +3.6% improved
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <DollarSign className="h-4 w-4 text-orange-600" />
                      <span className="text-sm font-medium">Carrying Cost</span>
                    </div>
                    <div className="text-xl font-bold">$180K</div>
                    <div className="flex items-center gap-1 text-xs text-green-600 mt-1">
                      <TrendingDown className="h-3 w-3" />
                      -25% reduced
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Optimization Insights & Results */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Inventory Optimization */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      Inventory Optimization Results
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="overflow-x-auto">
                        <table className="w-full text-xs">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-1">SKU</th>
                              <th className="text-left p-1">Current</th>
                              <th className="text-left p-1">Optimized</th>
                              <th className="text-left p-1">Turnover</th>
                            </tr>
                          </thead>
                          <tbody>
                            {inventoryData.map((item) => (
                              <tr key={item.sku} className="border-b text-xs">
                                <td className="p-1 font-medium">{item.sku}</td>
                                <td className="p-1">{item.stock}</td>
                                <td className="p-1 text-green-600">{Math.round(item.stock * 0.85)}</td>
                                <td className="p-1">{item.turnover}x</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Policy Optimization */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Sliders className="h-4 w-4" />
                      Policy Optimization Recommendations
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {policyData.map((policy, index) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-xs font-medium">{policy.policy}</span>
                            <Badge variant="outline" className="text-xs">{policy.impact}</Badge>
                          </div>
                          <div className="text-xs text-gray-600">
                            <span className="text-red-600">Current:</span> {policy.current}
                          </div>
                          <div className="text-xs text-gray-600">
                            <span className="text-green-600">Recommended:</span> {policy.recommended}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Optimization Insights */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Brain className="h-4 w-4" />
                    AI-Generated Optimization Insights
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Package className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-800">Inventory Insight</span>
                      </div>
                      <p className="text-xs text-blue-700">
                        SKU-004 shows highest optimization potential with 35% inventory reduction possible while maintaining 99% service level
                      </p>
                    </div>

                    <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Target className="h-4 w-4 text-purple-600" />
                        <span className="text-sm font-medium text-purple-800">Policy Insight</span>
                      </div>
                      <p className="text-xs text-purple-700">
                        Implementing dynamic safety stock policies could reduce carrying costs by $45K annually across all SKUs
                      </p>
                    </div>

                    <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Factory className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-green-800">Network Insight</span>
                      </div>
                      <p className="text-xs text-green-700">
                        Consolidating inventory at Atlanta DC would improve network efficiency by 18% while reducing operational complexity
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TransportationMap;
