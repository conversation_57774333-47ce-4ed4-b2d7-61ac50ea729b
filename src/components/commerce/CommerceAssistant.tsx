
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Bot,
  Send,
  Zap,
  TrendingUp,
  Package,
  MapPin,
  DollarSign,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { CommerceInsight } from "@/types/commerce";

const CommerceAssistant = () => {
  const [message, setMessage] = useState("");
  const [conversation, setConversation] = useState([
    {
      type: "assistant",
      message: "Hello! I'm your Commerce AI Assistant. I can help you with inventory management, pricing optimization, zone planning, and demand forecasting. What would you like to know?",
      timestamp: "10:30 AM"
    }
  ]);

  const [quickActions] = useState([
    { icon: Package, label: "Check low stock items", action: "restock" },
    { icon: DollarSign, label: "Optimize pricing for Zone 3", action: "pricing" },
    { icon: MapPin, label: "Expand delivery zones", action: "zones" },
    { icon: TrendingUp, label: "Forecast weekend demand", action: "forecast" }
  ]);

  const [activeInsights] = useState<CommerceInsight[]>([
    {
      id: "insight_001",
      type: "restock",
      title: "Restock Alert: Margherita Pizza",
      description: "Only 12 units left, predicted to run out by 2 PM based on current demand",
      priority: "high",
      impact: "Potential lost sales: $200",
      confidence: 94,
      action: "Order 30 more units",
      data: { skuId: "sku_001", currentStock: 12, predictedRunOut: "14:00" }
    },
    {
      id: "insight_002",
      type: "pricing",
      title: "Pricing Opportunity: Classic Burger",
      description: "Demand in Zone 3 allows for 15% price increase with minimal volume impact",
      priority: "medium",
      impact: "Additional revenue: $85/day",
      confidence: 87,
      action: "Increase price to $17.25",
      data: { skuId: "sku_002", suggestedPrice: 17.25, zone: "zone_003" }
    },
    {
      id: "insight_003",
      type: "zone-expansion",
      title: "Zone Expansion Opportunity",
      description: "High order density detected in area adjacent to Downtown Core",
      priority: "low",
      impact: "Potential 25 new orders/day",
      confidence: 72,
      action: "Create new micro-zone",
      data: { area: "Financial District", estimatedOrders: 25 }
    }
  ]);

  const handleSendMessage = () => {
    if (!message.trim()) return;

    const userMessage = {
      type: "user",
      message: message,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    // Simulate AI response
    const responses = [
      "Based on your current inventory levels, I recommend restocking Margherita Pizza within the next 2 hours to avoid stockouts.",
      "I've analyzed your Zone 3 performance - you can safely increase burger prices by 12-15% without significant demand drop.",
      "Your lunch rush typically starts at 11:45 AM. I suggest preparing 20% more inventory for your top 3 items.",
      "The weather forecast shows rain tomorrow, which typically increases delivery orders by 18%. Consider adjusting staff schedules."
    ];

    const assistantMessage = {
      type: "assistant", 
      message: responses[Math.floor(Math.random() * responses.length)],
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    setConversation(prev => [...prev, userMessage, assistantMessage]);
    setMessage("");
  };

  const handleQuickAction = (action: string) => {
    const actionMessages = {
      restock: "Here are your current low stock items: Margherita Pizza (12 left), Caesar Salad (8 left). Would you like me to create restock orders?",
      pricing: "I've identified pricing optimization opportunities in Zone 3. Classic Burger can be increased to $17.25 (+15%) and Caesar Salad to $14.50 (+12%).",
      zones: "Based on order density analysis, I recommend expanding into Financial District. Expected ROI: 180% within 3 months.",
      forecast: "Weekend forecast: 40% increase in pizza orders, 25% increase in burgers. Recommend stocking 60 pizzas and 45 burger patties."
    };

    const assistantMessage = {
      type: "assistant",
      message: actionMessages[action] || "I can help you with that request.",
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    setConversation(prev => [...prev, assistantMessage]);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800";
      case "medium": return "bg-yellow-100 text-yellow-800";
      case "low": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case "restock": return Package;
      case "pricing": return DollarSign;
      case "zone-expansion": return MapPin;
      case "demand-surge": return TrendingUp;
      default: return AlertTriangle;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Bot className="h-6 w-6" />
            AI Commerce Assistant
          </h2>
          <p className="text-sm text-gray-600">Context-aware recommendations and intelligent automation</p>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-gray-600">AI Active</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* AI Chat Interface */}
        <Card className="flex flex-col h-[600px]">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              Chat with AI Assistant
            </CardTitle>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col">
            {/* Quick Actions */}
            <div className="mb-4">
              <div className="text-sm font-medium text-gray-700 mb-2">Quick Actions:</div>
              <div className="grid grid-cols-2 gap-2">
                {quickActions.map((action, index) => {
                  const Icon = action.icon;
                  return (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuickAction(action.action)}
                      className="text-left justify-start h-auto p-2"
                    >
                      <Icon className="h-4 w-4 mr-2" />
                      <span className="text-xs">{action.label}</span>
                    </Button>
                  );
                })}
              </div>
            </div>

            {/* Chat Messages */}
            <div className="flex-1 overflow-y-auto space-y-3 mb-4 p-2 bg-gray-50 rounded-lg">
              {conversation.map((msg, index) => (
                <div key={index} className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-[80%] p-3 rounded-lg ${
                    msg.type === 'user' 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-white border'
                  }`}>
                    <div className="text-sm">{msg.message}</div>
                    <div className={`text-xs mt-1 ${
                      msg.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {msg.timestamp}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Message Input */}
            <div className="flex gap-2">
              <Input
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Ask about inventory, pricing, zones..."
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              />
              <Button onClick={handleSendMessage} size="sm">
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Active Insights & Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Active Insights & Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {activeInsights.map((insight) => {
              const Icon = getInsightIcon(insight.type);
              return (
                <div key={insight.id} className="p-4 border rounded-lg bg-gradient-to-r from-blue-50 to-purple-50">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Icon className="h-4 w-4 text-blue-600" />
                      <h4 className="font-medium text-sm">{insight.title}</h4>
                    </div>
                    <Badge className={getPriorityColor(insight.priority)}>
                      {insight.priority}
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3">{insight.description}</p>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center text-xs">
                      <span className="text-gray-500">Impact: {insight.impact}</span>
                      <span className="text-gray-500">{insight.confidence}% confidence</span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-blue-600">{insight.action}</span>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Apply
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}

            <div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <h4 className="font-medium text-green-800">Performance Summary</h4>
              </div>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <div className="text-green-700">Actions Taken Today</div>
                  <div className="font-bold text-green-800">7</div>
                </div>
                <div>
                  <div className="text-green-700">Estimated Savings</div>
                  <div className="font-bold text-green-800">$342</div>
                </div>
                <div>
                  <div className="text-green-700">Accuracy Rate</div>
                  <div className="font-bold text-green-800">94%</div>
                </div>
                <div>
                  <div className="text-green-700">Response Time</div>
                  <div className="font-bold text-green-800">1.2s</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CommerceAssistant;
