
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Package, 
  Plus, 
  Search, 
  Filter,
  Edit,
  ShoppingCart,
  Clock,
  DollarSign
} from "lucide-react";

const JnaMenuTab = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories = [
    { id: "all", name: "All Items", count: 324 },
    { id: "groceries", name: "Groceries", count: 186 },
    { id: "medicines", name: "Medicines", count: 67 },
    { id: "stationery", name: "Stationery", count: 41 },
    { id: "household", name: "Household", count: 30 }
  ];

  const skus = [
    {
      id: "sku_001",
      name: "Milk - Full Cream 1L",
      category: "groceries",
      price: 65,
      cost: 52,
      margin: 20,
      stock: 45,
      minStock: 20,
      status: "active",
      prepTime: 5,
      popularity: "high"
    },
    {
      id: "sku_002", 
      name: "Bread - White Loaf",
      category: "groceries",
      price: 25,
      cost: 18,
      margin: 28,
      stock: 8,
      minStock: 15,
      status: "low_stock",
      prepTime: 2,
      popularity: "high"
    },
    {
      id: "sku_003",
      name: "Paracetamol 500mg",
      category: "medicines", 
      price: 12,
      cost: 8,
      margin: 33,
      stock: 120,
      minStock: 50,
      status: "active",
      prepTime: 1,
      popularity: "medium"
    },
    {
      id: "sku_004",
      name: "A4 Paper Ream",
      category: "stationery",
      price: 280,
      cost: 220,
      margin: 21,
      stock: 0,
      minStock: 5,
      status: "out_of_stock",
      prepTime: 10,
      popularity: "low"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800";
      case "low_stock": return "bg-orange-100 text-orange-800";
      case "out_of_stock": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getPopularityColor = (popularity: string) => {
    switch (popularity) {
      case "high": return "bg-blue-100 text-blue-800";
      case "medium": return "bg-purple-100 text-purple-800";
      case "low": return "bg-gray-100 text-gray-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const filteredSKUs = skus.filter(sku => {
    const matchesSearch = sku.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || sku.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Package className="h-5 w-5" />
            JñaMenu - SKU Management
          </h2>
          <p className="text-sm text-gray-600">Manage your quick commerce inventory items</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add New SKU
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">324</div>
            <div className="text-sm text-gray-600">Total SKUs</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">289</div>
            <div className="text-sm text-gray-600">In Stock</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-600">23</div>
            <div className="text-sm text-gray-600">Low Stock</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">12</div>
            <div className="text-sm text-gray-600">Out of Stock</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search SKUs..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
            >
              {category.name} ({category.count})
            </Button>
          ))}
        </div>
      </div>

      {/* SKU Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredSKUs.map((sku) => (
          <Card key={sku.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-base">{sku.name}</CardTitle>
                  <p className="text-sm text-gray-600 capitalize">{sku.category}</p>
                </div>
                <Button variant="ghost" size="sm">
                  <Edit className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              {/* Status and Popularity */}
              <div className="flex gap-2">
                <Badge className={getStatusColor(sku.status)}>
                  {sku.status.replace('_', ' ')}
                </Badge>
                <Badge className={getPopularityColor(sku.popularity)}>
                  {sku.popularity} demand
                </Badge>
              </div>

              {/* Pricing */}
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-gray-400" />
                  <span>₹{sku.price}</span>
                </div>
                <div className="text-green-600">
                  {sku.margin}% margin
                </div>
              </div>

              {/* Stock Info */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Current Stock</span>
                  <span className={sku.stock <= sku.minStock ? "text-red-600 font-medium" : "text-gray-900"}>
                    {sku.stock} units
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${
                      sku.stock <= sku.minStock ? "bg-red-500" : "bg-green-500"
                    }`}
                    style={{ width: `${Math.min((sku.stock / (sku.minStock * 2)) * 100, 100)}%` }}
                  ></div>
                </div>
              </div>

              {/* Prep Time */}
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Clock className="h-4 w-4" />
                <span>{sku.prepTime} min prep</span>
              </div>

              {/* Actions */}
              <div className="pt-2 flex gap-2">
                <Button size="sm" variant="outline" className="flex-1">
                  <ShoppingCart className="h-4 w-4 mr-1" />
                  Reorder
                </Button>
                <Button size="sm" variant="outline">
                  Analytics
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default JnaMenuTab;
