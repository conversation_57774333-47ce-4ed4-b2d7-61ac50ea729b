
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DollarSign, Package, TrendingUp, Target } from "lucide-react";

const JnaPriceTab = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            JñaPrice - Dynamic Pricing
          </h2>
          <p className="text-sm text-gray-600">Smart pricing, bundling, and category-based markdowns</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Dynamic Pricing
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Real-time price optimization based on demand</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Bundle Offers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Create and manage product bundles automatically</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Category Markdowns
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Intelligent category-based pricing strategies</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default JnaPriceTab;
