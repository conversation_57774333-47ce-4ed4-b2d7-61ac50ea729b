
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  DollarSign,
  Package,
  TrendingUp,
  Target,
  Percent,
  Clock,
  Zap,
  Settings,
  BarChart3,
  AlertTriangle
} from "lucide-react";

const JnaPriceTab = () => {
  const [autoPricingEnabled, setAutoPricingEnabled] = useState(true);

  const pricingRules = [
    {
      id: 1,
      name: "Lunch Hour Surge",
      type: "surge",
      category: "All Items",
      multiplier: 1.2,
      timeRange: "12:00 PM - 2:00 PM",
      isActive: true,
      revenue: "+₹12,450"
    },
    {
      id: 2,
      name: "Evening Discount",
      type: "discount",
      category: "Dairy Products",
      multiplier: 0.9,
      timeRange: "6:00 PM - 8:00 PM",
      isActive: true,
      revenue: "-₹3,200"
    },
    {
      id: 3,
      name: "Weekend Bundle",
      type: "bundle",
      category: "Groceries",
      multiplier: 0.85,
      timeRange: "Sat-Sun",
      isActive: false,
      revenue: "+₹8,900"
    }
  ];

  const priceOptimizations = [
    {
      product: "Milk - Full Cream 1L",
      currentPrice: 65,
      suggestedPrice: 68,
      reason: "High demand in Zone 3",
      impact: "+12% revenue",
      confidence: 89
    },
    {
      product: "Bread - White Loaf",
      currentPrice: 25,
      suggestedPrice: 23,
      reason: "Low stock clearance",
      impact: "+8% velocity",
      confidence: 76
    },
    {
      product: "Bananas - 1kg",
      currentPrice: 45,
      suggestedPrice: 48,
      reason: "Seasonal demand spike",
      impact: "+15% margin",
      confidence: 92
    }
  ];

  const getRuleTypeColor = (type: string) => {
    switch (type) {
      case 'surge': return 'bg-red-100 text-red-800';
      case 'discount': return 'bg-green-100 text-green-800';
      case 'bundle': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 85) return 'text-green-600';
    if (confidence >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Dynamic Pricing Engine
          </h2>
          <p className="text-sm text-gray-600">AI-powered pricing optimization and revenue management</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm">Auto-Pricing</span>
            <Switch
              checked={autoPricingEnabled}
              onCheckedChange={setAutoPricingEnabled}
            />
          </div>
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            Configure Rules
          </Button>
        </div>
      </div>

      {/* Pricing Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Revenue Impact
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">+₹18.1K</div>
            <p className="text-xs text-muted-foreground">This week from pricing</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Percent className="h-4 w-4" />
              Avg Margin
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24.8%</div>
            <p className="text-xs text-muted-foreground">+2.3% from last week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Active Rules
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">12</div>
            <p className="text-xs text-muted-foreground">Pricing rules running</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Target className="h-4 w-4" />
              Price Changes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">47</div>
            <p className="text-xs text-muted-foreground">Auto-adjustments today</p>
          </CardContent>
        </Card>
      </div>

      {/* AI Price Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            AI Price Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {priceOptimizations.map((item, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{item.product}</h4>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className={getConfidenceColor(item.confidence)}>
                      {item.confidence}% confidence
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Current: </span>
                    <span className="font-medium">₹{item.currentPrice}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Suggested: </span>
                    <span className="font-medium text-blue-600">₹{item.suggestedPrice}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Reason: </span>
                    <span>{item.reason}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Impact: </span>
                    <span className="font-medium text-green-600">{item.impact}</span>
                  </div>
                </div>

                <div className="flex justify-end gap-2 mt-3">
                  <Button size="sm" variant="outline">
                    Decline
                  </Button>
                  <Button size="sm">
                    Apply Price
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Pricing Rules */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Active Pricing Rules
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {pricingRules.map((rule) => (
              <div key={rule.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <h4 className="font-medium">{rule.name}</h4>
                    <Badge className={getRuleTypeColor(rule.type)}>
                      {rule.type.charAt(0).toUpperCase() + rule.type.slice(1)}
                    </Badge>
                    {rule.isActive ? (
                      <Badge className="bg-green-100 text-green-800">Active</Badge>
                    ) : (
                      <Badge variant="outline">Inactive</Badge>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{rule.revenue}</div>
                    <div className="text-sm text-gray-600">Revenue impact</div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Category: </span>
                    <span>{rule.category}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Multiplier: </span>
                    <span className="font-medium">{rule.multiplier}x</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Time: </span>
                    <span>{rule.timeRange}</span>
                  </div>
                </div>

                <div className="flex justify-end gap-2 mt-3">
                  <Button size="sm" variant="outline">
                    Edit Rule
                  </Button>
                  <Button size="sm" variant="outline">
                    View Analytics
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Price Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <Percent className="h-4 w-4 mr-2" />
                Apply Bulk Discount
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Package className="h-4 w-4 mr-2" />
                Create Bundle Offer
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <TrendingUp className="h-4 w-4 mr-2" />
                Surge Pricing for Zone
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Emergency Price Override
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Price Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { metric: "Revenue per Order", value: "₹224", change: "+8.2%" },
                { metric: "Margin per SKU", value: "24.8%", change: "+2.1%" },
                { metric: "Price Elasticity", value: "0.73", change: "-0.05%" },
                { metric: "Conversion Rate", value: "12.4%", change: "+1.8%" }
              ].map((metric, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <span className="text-sm font-medium">{metric.metric}</span>
                  <div className="text-right">
                    <div className="font-medium">{metric.value}</div>
                    <div className="text-sm text-green-600">{metric.change}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default JnaPriceTab;
