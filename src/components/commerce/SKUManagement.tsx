
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Plus, 
  Search, 
  Filter, 
  Package,
  Edit,
  Trash2,
  Clock,
  AlertTriangle
} from "lucide-react";
import { SKU } from "@/types/commerce";

const SKUManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [skus] = useState<SKU[]>([
    {
      id: "sku_001",
      name: "Margherita Pizza",
      category: "Pizza",
      price: 18.99,
      cost: 8.50,
      margin: 55.2,
      isPerishable: true,
      prepTime: 12,
      shelfLife: 2,
      status: "active",
      tags: ["vegetarian", "popular"],
      createdAt: "2024-01-15",
      updatedAt: "2024-01-20",
      stockLevel: 12,
      minStockLevel: 10,
      maxStockLevel: 50
    },
    {
      id: "sku_002",
      name: "Classic Burger",
      category: "Burgers",
      price: 14.99,
      cost: 6.75,
      margin: 55.0,
      isPerishable: true,
      prepTime: 8,
      shelfLife: 1,
      status: "active",
      tags: ["bestseller"],
      createdAt: "2024-01-10",
      updatedAt: "2024-01-18",
      stockLevel: 25,
      minStockLevel: 15,
      maxStockLevel: 40
    },
    {
      id: "sku_003",
      name: "Caesar Salad",
      category: "Salads",
      price: 12.99,
      cost: 4.20,
      margin: 67.7,
      isPerishable: true,
      prepTime: 5,
      shelfLife: 4,
      status: "active",
      tags: ["healthy", "vegetarian"],
      createdAt: "2024-01-12",
      updatedAt: "2024-01-19",
      stockLevel: 8,
      minStockLevel: 5,
      maxStockLevel: 30
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800";
      case "inactive": return "bg-gray-100 text-gray-800";
      case "draft": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStockStatus = (sku: SKU) => {
    if (!sku.stockLevel || !sku.minStockLevel) return null;
    if (sku.stockLevel <= sku.minStockLevel) {
      return { color: "text-red-600", icon: AlertTriangle, label: "Low Stock" };
    }
    return { color: "text-green-600", label: "In Stock" };
  };

  const filteredSKUs = skus.filter(sku =>
    sku.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    sku.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search SKUs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add SKU
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{skus.length}</div>
            <div className="text-sm text-gray-600">Total SKUs</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {skus.filter(s => s.status === 'active').length}
            </div>
            <div className="text-sm text-gray-600">Active SKUs</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-600">
              {skus.filter(s => getStockStatus(s)?.label === 'Low Stock').length}
            </div>
            <div className="text-sm text-gray-600">Low Stock Items</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">
              {Math.round(skus.reduce((acc, s) => acc + s.margin, 0) / skus.length)}%
            </div>
            <div className="text-sm text-gray-600">Avg Margin</div>
          </CardContent>
        </Card>
      </div>

      {/* SKU Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            SKU Inventory
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>SKU Details</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Pricing</TableHead>
                <TableHead>Stock Status</TableHead>
                <TableHead>Prep Time</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSKUs.map((sku) => {
                const stockStatus = getStockStatus(sku);
                return (
                  <TableRow key={sku.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{sku.name}</div>
                        <div className="text-sm text-gray-500 flex items-center gap-1">
                          {sku.isPerishable && (
                            <Clock className="h-3 w-3" />
                          )}
                          {sku.tags.map(tag => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{sku.category}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">${sku.price}</div>
                        <div className="text-sm text-gray-500">
                          {sku.margin.toFixed(1)}% margin
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {stockStatus?.icon && (
                          <stockStatus.icon className={`h-4 w-4 ${stockStatus.color}`} />
                        )}
                        <div>
                          <div className={stockStatus?.color || "text-gray-600"}>
                            {sku.stockLevel} units
                          </div>
                          <div className="text-xs text-gray-500">
                            {stockStatus?.label}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{sku.prepTime} min</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(sku.status)}>
                        {sku.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default SKUManagement;
