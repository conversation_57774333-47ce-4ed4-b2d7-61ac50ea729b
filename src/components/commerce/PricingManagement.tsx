
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Plus, 
  DollarSign,
  TrendingUp,
  Percent,
  Gift,
  Clock,
  Target
} from "lucide-react";
import { PricingRule } from "@/types/commerce";

const PricingManagement = () => {
  const [pricingRules] = useState<PricingRule[]>([
    {
      id: "rule_001",
      name: "Lunch Hour Surge",
      type: "surge",
      conditions: {
        timeOfDay: ["12:00", "14:00"],
        dayOfWeek: ["monday", "tuesday", "wednesday", "thursday", "friday"]
      },
      action: {
        surgeMultiplier: 1.2
      },
      isActive: true,
      validFrom: "2024-01-01",
      validTo: "2024-12-31",
      priority: 1
    },
    {
      id: "rule_002", 
      name: "Weekend Bundle Deal",
      type: "bundle",
      conditions: {
        dayOfWeek: ["saturday", "sunday"],
        minOrderValue: 25
      },
      action: {
        discountPercent: 15,
        bundleSKUs: ["sku_001", "sku_002"]
      },
      isActive: true,
      validFrom: "2024-01-01",
      validTo: "2024-12-31",
      priority: 2
    },
    {
      id: "rule_003",
      name: "Buy 2 Get 1 Free Pizza",
      type: "buy-x-get-y",
      conditions: {
        skuIds: ["sku_001"]
      },
      action: {
        freeItemId: "sku_001"
      },
      isActive: false,
      validFrom: "2024-02-01",
      validTo: "2024-02-28",
      priority: 3
    }
  ]);

  const getRuleTypeIcon = (type: string) => {
    switch (type) {
      case "surge": return TrendingUp;
      case "discount": return Percent;
      case "bundle": return Gift;
      case "buy-x-get-y": return Target;
      default: return DollarSign;
    }
  };

  const getRuleTypeColor = (type: string) => {
    switch (type) {
      case "surge": return "bg-red-100 text-red-800";
      case "discount": return "bg-green-100 text-green-800";
      case "bundle": return "bg-blue-100 text-blue-800";
      case "buy-x-get-y": return "bg-purple-100 text-purple-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const formatConditions = (conditions: PricingRule['conditions']) => {
    const parts = [];
    if (conditions.timeOfDay) {
      parts.push(`${conditions.timeOfDay[0]}-${conditions.timeOfDay[1]}`);
    }
    if (conditions.dayOfWeek) {
      parts.push(conditions.dayOfWeek.join(", "));
    }
    if (conditions.minOrderValue) {
      parts.push(`Min $${conditions.minOrderValue}`);
    }
    return parts.join(" • ");
  };

  const formatAction = (action: PricingRule['action'], type: string) => {
    switch (type) {
      case "surge":
        return `${((action.surgeMultiplier || 1) * 100 - 100)}% surge`;
      case "discount":
        return action.discountPercent 
          ? `${action.discountPercent}% off`
          : `$${action.discountAmount} off`;
      case "bundle":
        return `${action.discountPercent}% bundle discount`;
      case "buy-x-get-y":
        return "Buy 2 Get 1 Free";
      default:
        return "Custom rule";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Dynamic Pricing & Offers</h2>
          <p className="text-sm text-gray-600">Manage pricing rules, surge pricing, and promotional offers</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create Rule
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">
              {pricingRules.filter(r => r.isActive).length}
            </div>
            <div className="text-sm text-gray-600">Active Rules</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {pricingRules.filter(r => r.type === 'discount').length}
            </div>
            <div className="text-sm text-gray-600">Discount Rules</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">
              {pricingRules.filter(r => r.type === 'surge').length}
            </div>
            <div className="text-sm text-gray-600">Surge Rules</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">$847</div>
            <div className="text-sm text-gray-600">Revenue from Rules</div>
          </CardContent>
        </Card>
      </div>

      {/* Active Rules Quick View */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Currently Active Rules
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {pricingRules.filter(rule => rule.isActive).map((rule) => {
              const Icon = getRuleTypeIcon(rule.type);
              return (
                <div key={rule.id} className="p-4 border rounded-lg bg-gradient-to-br from-blue-50 to-purple-50">
                  <div className="flex items-center gap-2 mb-2">
                    <Icon className="h-4 w-4 text-blue-600" />
                    <Badge className={getRuleTypeColor(rule.type)}>
                      {rule.type}
                    </Badge>
                  </div>
                  <div className="font-medium mb-1">{rule.name}</div>
                  <div className="text-sm text-gray-600 mb-2">
                    {formatConditions(rule.conditions)}
                  </div>
                  <div className="text-sm font-medium text-blue-600">
                    {formatAction(rule.action, rule.type)}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* All Pricing Rules */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            All Pricing Rules
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Rule Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Conditions</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {pricingRules.map((rule) => {
                const Icon = getRuleTypeIcon(rule.type);
                return (
                  <TableRow key={rule.id}>
                    <TableCell>
                      <div className="font-medium">{rule.name}</div>
                      <div className="text-sm text-gray-500">
                        Valid: {rule.validFrom} to {rule.validTo}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        <Badge className={getRuleTypeColor(rule.type)}>
                          {rule.type}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell className="max-w-xs">
                      <div className="text-sm">
                        {formatConditions(rule.conditions)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {formatAction(rule.action, rule.type)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">P{rule.priority}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={rule.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
                        {rule.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm">
                          Edit
                        </Button>
                        <Button variant="ghost" size="sm">
                          {rule.isActive ? "Disable" : "Enable"}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default PricingManagement;
