
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, MessageSquare, Zap, Target } from "lucide-react";

const JnaAssistantTab = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Bot className="h-5 w-5" />
            JñaAssistant - AI Assistant
          </h2>
          <p className="text-sm text-gray-600">Contextual AI prompts for business operations</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Smart Prompts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Contextual suggestions like "Reorder milk in Zone 5"</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">One-click execution of AI recommendations</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Business Intelligence
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Natural language queries for business insights</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default JnaAssistantTab;
