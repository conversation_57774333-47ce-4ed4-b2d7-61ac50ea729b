
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  TrendingUp,
  Brain,
  Target,
  AlertTriangle,
  RefreshCw,
  BarChart3,
  Lightbulb
} from "lucide-react";
import { DemandForecast } from "@/types/commerce";

const DemandForecasting = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [forecasts] = useState<DemandForecast[]>([
    {
      skuId: "sku_001",
      skuName: "Margherita Pizza",
      predictedDemand: 45,
      confidence: 92,
      period: "daily",
      factors: ["weather", "weekday", "local events"],
      recommendations: ["Increase inventory by 20%", "Prepare for lunch rush"]
    },
    {
      skuId: "sku_002", 
      skuName: "Classic Burger",
      predictedDemand: 38,
      confidence: 87,
      period: "daily",
      factors: ["promotions", "competitor pricing", "seasonality"],
      recommendations: ["Stock up on patties", "Consider promotional pricing"]
    },
    {
      skuId: "sku_003",
      skuName: "Caesar Salad",
      predictedDemand: 22,
      confidence: 78,
      period: "daily",
      factors: ["health trends", "lunch hours", "weather"],
      recommendations: ["Prepare fresh ingredients", "Market as healthy option"]
    }
  ]);

  const [insights] = useState([
    {
      type: "opportunity",
      title: "High Demand Period Detected", 
      description: "AI predicts 35% increase in pizza orders during 6-8 PM today",
      confidence: 94,
      impact: "High"
    },
    {
      type: "warning",
      title: "Potential Stock Shortage",
      description: "Caesar Salad may run out by 3 PM based on current demand trajectory",
      confidence: 82,
      impact: "Medium"
    },
    {
      type: "recommendation",
      title: "New Menu Item Suggestion",
      description: "Consider adding Chicken Sandwich based on market analysis",
      confidence: 76,
      impact: "Low"
    }
  ]);

  const generateNewForecast = async () => {
    setIsGenerating(true);
    // Simulate AI processing
    setTimeout(() => {
      setIsGenerating(false);
    }, 3000);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return "text-green-600";
    if (confidence >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case "opportunity": return TrendingUp;
      case "warning": return AlertTriangle;
      case "recommendation": return Lightbulb;
      default: return Brain;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case "opportunity": return "bg-green-50 border-green-200";
      case "warning": return "bg-orange-50 border-orange-200";
      case "recommendation": return "bg-blue-50 border-blue-200";
      default: return "bg-gray-50 border-gray-200";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">JñaPredict Demand Forecasting</h2>
          <p className="text-sm text-gray-600">AI-powered demand predictions and menu recommendations</p>
        </div>
        <Button 
          onClick={generateNewForecast}
          disabled={isGenerating}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${isGenerating ? 'animate-spin' : ''}`} />
          {isGenerating ? 'Analyzing...' : 'Generate Forecast'}
        </Button>
      </div>

      {/* Forecast Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">
              {forecasts.reduce((acc, f) => acc + f.predictedDemand, 0)}
            </div>
            <div className="text-sm text-gray-600">Total Predicted Orders</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {Math.round(forecasts.reduce((acc, f) => acc + f.confidence, 0) / forecasts.length)}%
            </div>
            <div className="text-sm text-gray-600">Avg Confidence</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">
              {forecasts.filter(f => f.confidence >= 80).length}
            </div>
            <div className="text-sm text-gray-600">High Confidence Items</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-600">3</div>
            <div className="text-sm text-gray-600">AI Insights</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Demand Forecasts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Today's Demand Forecast
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {forecasts.map((forecast) => (
              <div key={forecast.skuId} className="p-4 border rounded-lg">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-medium">{forecast.skuName}</h4>
                    <div className="text-2xl font-bold text-blue-600 mt-1">
                      {forecast.predictedDemand} orders
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`font-medium ${getConfidenceColor(forecast.confidence)}`}>
                      {forecast.confidence}%
                    </div>
                    <div className="text-xs text-gray-500">confidence</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-1">Key Factors:</div>
                    <div className="flex flex-wrap gap-1">
                      {forecast.factors.map((factor) => (
                        <Badge key={factor} variant="outline" className="text-xs">
                          {factor}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-1">Recommendations:</div>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {forecast.recommendations.map((rec, index) => (
                        <li key={index} className="flex items-center gap-2">
                          <Target className="h-3 w-3" />
                          {rec}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* AI Insights */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI Market Insights
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {insights.map((insight, index) => {
              const Icon = getInsightIcon(insight.type);
              return (
                <div key={index} className={`p-4 border rounded-lg ${getInsightColor(insight.type)}`}>
                  <div className="flex items-start gap-3">
                    <Icon className="h-5 w-5 mt-0.5 text-gray-600" />
                    <div className="flex-1">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium">{insight.title}</h4>
                        <Badge variant="outline">{insight.confidence}%</Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{insight.description}</p>
                      <div className="flex justify-between items-center">
                        <Badge className={
                          insight.impact === 'High' ? 'bg-red-100 text-red-800' :
                          insight.impact === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }>
                          {insight.impact} Impact
                        </Badge>
                        <Button variant="outline" size="sm">
                          Take Action
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}

            <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200">
              <div className="flex items-center gap-2 mb-2">
                <Lightbulb className="h-5 w-5 text-purple-600" />
                <h4 className="font-medium text-purple-800">Menu Optimization Suggestion</h4>
              </div>
              <p className="text-sm text-purple-700 mb-3">
                Based on demand patterns and market trends, consider adding these items to your menu:
              </p>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Chicken Avocado Wrap</span>
                  <Badge className="bg-purple-100 text-purple-800">76% success probability</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Spicy Tuna Bowl</span>
                  <Badge className="bg-purple-100 text-purple-800">68% success probability</Badge>
                </div>
              </div>
              <Button className="w-full mt-3" variant="outline">
                Analyze New Menu Items
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DemandForecasting;
