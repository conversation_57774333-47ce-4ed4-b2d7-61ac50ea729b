
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { MapPin, Truck, Clock, Target } from "lucide-react";

const JnaRouteTab = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            JñaRoute - Operations View
          </h2>
          <p className="text-sm text-gray-600">Live delivery route planning and dispatch management</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5" />
              Route Planning
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Optimize delivery routes in real-time</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Live Tracking
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Monitor all active deliveries and ETAs</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Dispatch Control
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Intelligent dispatch assignment and management</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default JnaRouteTab;
