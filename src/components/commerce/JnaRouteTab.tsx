
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  MapPin,
  Truck,
  Clock,
  Target,
  Navigation,
  Users,
  Route,
  Activity,
  RefreshCw,
  Phone,
  MessageSquare
} from "lucide-react";

const JnaRouteTab = () => {
  const [selectedRoute, setSelectedRoute] = useState("route-1");

  const deliveryStats = {
    activeDeliveries: 23,
    completedToday: 89,
    avgDeliveryTime: "23 min",
    onTimeRate: "94.2%"
  };

  const activeRoutes = [
    {
      id: "route-1",
      driver: "<PERSON><PERSON>",
      vehicle: "DL-8C-1234",
      zone: "Zone 3",
      orders: 8,
      completed: 5,
      remaining: 3,
      estimatedCompletion: "2:30 PM",
      currentLocation: "Sector 15",
      status: "active"
    },
    {
      id: "route-2",
      driver: "<PERSON><PERSON> <PERSON>",
      vehicle: "DL-9A-5678",
      zone: "Zone 1",
      orders: 6,
      completed: 6,
      remaining: 0,
      estimatedCompletion: "Completed",
      currentLocation: "Returning to hub",
      status: "completed"
    },
    {
      id: "route-3",
      driver: "Vikash Yadav",
      vehicle: "DL-7B-9012",
      zone: "Zone 2",
      orders: 10,
      completed: 2,
      remaining: 8,
      estimatedCompletion: "3:45 PM",
      currentLocation: "Cyber City",
      status: "active"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'delayed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getProgressPercentage = (completed: number, total: number) => {
    return total > 0 ? (completed / total) * 100 : 0;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Delivery & Route Management
          </h2>
          <p className="text-sm text-gray-600">Real-time delivery tracking and route optimization</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Routes
          </Button>
          <Button variant="outline">
            <Target className="h-4 w-4 mr-2" />
            Optimize Routes
          </Button>
        </div>
      </div>

      {/* Delivery Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Active Deliveries
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{deliveryStats.activeDeliveries}</div>
            <p className="text-xs text-muted-foreground">Currently on road</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Target className="h-4 w-4" />
              Completed Today
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{deliveryStats.completedToday}</div>
            <p className="text-xs text-muted-foreground">Successful deliveries</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Avg Delivery Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{deliveryStats.avgDeliveryTime}</div>
            <p className="text-xs text-muted-foreground">From dispatch to delivery</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Route className="h-4 w-4" />
              On-Time Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{deliveryStats.onTimeRate}</div>
            <p className="text-xs text-muted-foreground">Within promised time</p>
          </CardContent>
        </Card>
      </div>

      {/* Active Routes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Navigation className="h-5 w-5" />
            Active Delivery Routes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {activeRoutes.map((route) => (
              <div key={route.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">{route.driver}</span>
                    </div>
                    <Badge variant="outline">{route.vehicle}</Badge>
                    <Badge className={getStatusColor(route.status)}>
                      {route.status.charAt(0).toUpperCase() + route.status.slice(1)}
                    </Badge>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      <Phone className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <MessageSquare className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <MapPin className="h-4 w-4" />
                      Track
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                  <div className="text-sm">
                    <span className="text-gray-600">Zone: </span>
                    <span className="font-medium">{route.zone}</span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-600">Current Location: </span>
                    <span className="font-medium">{route.currentLocation}</span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-600">ETA: </span>
                    <span className="font-medium">{route.estimatedCompletion}</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Delivery Progress</span>
                    <span>{route.completed}/{route.orders} orders completed</span>
                  </div>
                  <Progress
                    value={getProgressPercentage(route.completed, route.orders)}
                    className="h-2"
                  />
                  {route.remaining > 0 && (
                    <div className="text-sm text-gray-600">
                      {route.remaining} orders remaining
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Zone Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Zone Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { zone: "Zone 1", orders: 34, avgTime: "21 min", efficiency: 96 },
                { zone: "Zone 2", orders: 28, avgTime: "25 min", efficiency: 92 },
                { zone: "Zone 3", orders: 31, avgTime: "19 min", efficiency: 98 }
              ].map((zone, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium">{zone.zone}</div>
                    <div className="text-sm text-gray-600">{zone.orders} orders today</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{zone.avgTime}</div>
                    <div className="text-sm text-gray-600">{zone.efficiency}% efficiency</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <Target className="h-4 w-4 mr-2" />
                Optimize All Routes
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Users className="h-4 w-4 mr-2" />
                Assign Emergency Delivery
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <MapPin className="h-4 w-4 mr-2" />
                View Live Map
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Activity className="h-4 w-4 mr-2" />
                Generate Route Report
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default JnaRouteTab;
