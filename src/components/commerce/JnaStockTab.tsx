
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Package, TrendingUp, AlertTriangle, BarChart3 } from "lucide-react";

const JnaStockTab = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Package className="h-5 w-5" />
            JñaStock - Inventory Management
          </h2>
          <p className="text-sm text-gray-600">Real-time stock tracking with per-SKU predictions</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Stock Levels
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Real-time inventory tracking across all zones</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Low Stock Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Automated reorder suggestions based on demand patterns</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Stock Predictions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Per-SKU demand forecasting for optimal inventory</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default JnaStockTab;
