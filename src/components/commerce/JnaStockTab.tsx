
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Package, TrendingUp, <PERSON><PERSON><PERSON><PERSON>gle, BarChart3, RefreshCw, Download, MapPin, Clock } from "lucide-react";

const JnaStockTab = () => {
  const [refreshing, setRefreshing] = useState(false);

  const stockData = [
    {
      id: 1,
      name: "Milk - Full Cream 1L",
      category: "Dairy",
      currentStock: 45,
      minStock: 20,
      maxStock: 100,
      zone: "Zone 3",
      lastUpdated: "2 min ago",
      status: "healthy",
      velocity: 12 // units per hour
    },
    {
      id: 2,
      name: "Bread - White Loaf",
      category: "Bakery",
      currentStock: 8,
      minStock: 15,
      maxStock: 50,
      zone: "Zone 1",
      lastUpdated: "5 min ago",
      status: "low",
      velocity: 8
    },
    {
      id: 3,
      name: "<PERSON><PERSON><PERSON> - 1kg",
      category: "Fruits",
      currentStock: 0,
      minStock: 10,
      maxStock: 30,
      zone: "Zone 2",
      lastUpdated: "1 min ago",
      status: "out",
      velocity: 15
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-100 text-green-800';
      case 'low': return 'bg-yellow-100 text-yellow-800';
      case 'out': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStockPercentage = (current: number, max: number) => {
    return Math.min((current / max) * 100, 100);
  };

  const handleRefresh = () => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 2000);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Package className="h-5 w-5" />
            Inventory Management
          </h2>
          <p className="text-sm text-gray-600">Real-time stock tracking across all zones with automated alerts</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Package className="h-4 w-4" />
              Total SKUs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">324</div>
            <p className="text-xs text-muted-foreground">Across all zones</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              Low Stock Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">23</div>
            <p className="text-xs text-muted-foreground">Need reordering</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Stock Value
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹2.4L</div>
            <p className="text-xs text-muted-foreground">Current inventory value</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Turnover Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8.7x</div>
            <p className="text-xs text-muted-foreground">Monthly average</p>
          </CardContent>
        </Card>
      </div>

      {/* Stock Items */}
      <Card>
        <CardHeader>
          <CardTitle>Stock Levels by Item</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stockData.map((item) => (
              <div key={item.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium">{item.name}</h4>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span>{item.category}</span>
                      <span className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {item.zone}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {item.lastUpdated}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge className={getStatusColor(item.status)}>
                      {item.status === 'healthy' ? 'In Stock' : item.status === 'low' ? 'Low Stock' : 'Out of Stock'}
                    </Badge>
                    <div className="text-sm text-gray-600 mt-1">
                      {item.velocity} units/hr velocity
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Current: {item.currentStock} units</span>
                    <span>Min: {item.minStock} | Max: {item.maxStock}</span>
                  </div>
                  <Progress
                    value={getStockPercentage(item.currentStock, item.maxStock)}
                    className="h-2"
                  />
                  {item.currentStock <= item.minStock && (
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-sm text-red-600">Reorder recommended</span>
                      <Button size="sm" variant="outline">
                        Reorder Now
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default JnaStockTab;
