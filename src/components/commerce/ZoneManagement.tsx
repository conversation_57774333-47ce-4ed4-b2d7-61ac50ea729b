import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  MapPin,
  Plus,
  Settings,
  Truck,
  Clock,
  DollarSign,
  Users,
  BarChart3
} from "lucide-react";
import { Zone } from "@/types/commerce";

const ZoneManagement = () => {
  const [zones] = useState<Zone[]>([
    {
      id: "zone_001",
      name: "Downtown Core",
      type: "delivery",
      coordinates: [
        { lat: 40.7589, lng: -73.9851 },
        { lat: 40.7614, lng: -73.9776 },
        { lat: 40.7505, lng: -73.9934 }
      ],
      maxDeliveryTime: 30,
      minOrderValue: 15,
      deliveryFee: 2.99,
      isActive: true,
      capacityLimit: 50,
      currentOrders: 23
    },
    {
      id: "zone_002", 
      name: "Midtown District",
      type: "hybrid",
      coordinates: [
        { lat: 40.7505, lng: -73.9934 },
        { lat: 40.7614, lng: -73.9776 },
        { lat: 40.7282, lng: -73.9942 }
      ],
      maxDeliveryTime: 45,
      minOrderValue: 20,
      deliveryFee: 3.99,
      isActive: true,
      capacityLimit: 75,
      currentOrders: 31
    },
    {
      id: "zone_003",
      name: "Brooklyn Heights",
      type: "delivery",
      coordinates: [
        { lat: 40.6955, lng: -73.9927 },
        { lat: 40.7022, lng: -73.9860 },
        { lat: 40.6892, lng: -74.0021 }
      ],
      maxDeliveryTime: 40,
      minOrderValue: 18,
      deliveryFee: 4.99,
      isActive: false,
      capacityLimit: 30,
      currentOrders: 0
    }
  ]);

  const getZoneTypeColor = (type: string) => {
    switch (type) {
      case "delivery": return "bg-blue-100 text-blue-800";
      case "pickup": return "bg-green-100 text-green-800";
      case "hybrid": return "bg-purple-100 text-purple-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getCapacityColor = (current: number, max: number) => {
    const percentage = (current / max) * 100;
    if (percentage >= 90) return "text-red-600";
    if (percentage >= 70) return "text-orange-600";
    return "text-green-600";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Zone & Route Management</h2>
          <p className="text-sm text-gray-600">Optimize delivery zones and routing efficiency</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            Auto-Optimize
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Zone
          </Button>
        </div>
      </div>

      {/* Zone Performance Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">
              {zones.filter(z => z.isActive).length}
            </div>
            <div className="text-sm text-gray-600">Active Zones</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {zones.reduce((acc, z) => acc + z.currentOrders, 0)}
            </div>
            <div className="text-sm text-gray-600">Active Orders</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">
              {Math.round(zones.reduce((acc, z) => acc + z.maxDeliveryTime, 0) / zones.length)} min
            </div>
            <div className="text-sm text-gray-600">Avg Delivery Time</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-600">
              ${(zones.reduce((acc, z) => acc + z.deliveryFee, 0) / zones.length).toFixed(2)}
            </div>
            <div className="text-sm text-gray-600">Avg Delivery Fee</div>
          </CardContent>
        </Card>
      </div>

      {/* Zone List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Delivery Zones
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {zones.map((zone) => (
                <div key={zone.id} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium">{zone.name}</h4>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge className={getZoneTypeColor(zone.type)}>
                          {zone.type}
                        </Badge>
                        <Badge className={zone.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
                          {zone.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>{zone.maxDeliveryTime} min</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-gray-400" />
                      <span>${zone.deliveryFee}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Truck className="h-4 w-4 text-gray-400" />
                      <span>Min: ${zone.minOrderValue}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-gray-400" />
                      <span className={getCapacityColor(zone.currentOrders, zone.capacityLimit)}>
                        {zone.currentOrders}/{zone.capacityLimit}
                      </span>
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">Capacity Usage</span>
                      <span className={getCapacityColor(zone.currentOrders, zone.capacityLimit)}>
                        {Math.round((zone.currentOrders / zone.capacityLimit) * 100)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${(zone.currentOrders / zone.capacityLimit) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Zone Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {zones.filter(z => z.isActive).map((zone) => (
                  <div key={zone.id} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">{zone.name}</span>
                      <Badge variant="outline">
                        {zone.currentOrders} orders
                      </Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-3 text-sm">
                      <div>
                        <div className="text-gray-500">Efficiency</div>
                        <div className="font-medium text-green-600">94%</div>
                      </div>
                      <div>
                        <div className="text-gray-500">On-Time</div>
                        <div className="font-medium text-blue-600">97%</div>
                      </div>
                      <div>
                        <div className="text-gray-500">Revenue</div>
                        <div className="font-medium text-purple-600">$1,247</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Zone Optimization Suggestions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="font-medium text-blue-800">Expand Downtown Core</div>
                  <div className="text-sm text-blue-600">High demand in adjacent areas suggests expansion potential</div>
                </div>
                <div className="p-3 bg-orange-50 rounded-lg">
                  <div className="font-medium text-orange-800">Adjust Midtown Capacity</div>
                  <div className="text-sm text-orange-600">Consider increasing capacity limit to handle peak demand</div>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="font-medium text-green-800">Reactivate Brooklyn Heights</div>
                  <div className="text-sm text-green-600">Market conditions improved, consider reactivating this zone</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ZoneManagement;
