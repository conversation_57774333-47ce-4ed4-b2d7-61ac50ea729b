
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, AlertTriangle } from "lucide-react";

const ONDCGatewayTab = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Link className="h-5 w-5" />
            ONDC Gateway
          </h2>
          <p className="text-sm text-gray-600">Handles order lifecycle callbacks (/on_search, /on_track, etc.)</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Order Callbacks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Handle /on_search, /on_select, /on_init callbacks</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Status Tracking
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Manage /on_track, /on_status lifecycle events</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Error Handling
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Robust error handling and retry mechanisms</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ONDCGatewayTab;
