
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart3, TrendingUp, Target, DollarSign } from "lucide-react";

const JnaAnalyticsTab = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            JñaAnalytics - Business Intelligence
          </h2>
          <p className="text-sm text-gray-600">KPI dashboards, revenue tracking, and performance trends</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Revenue Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Comprehensive revenue tracking and forecasting</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Demand Spikes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Identify and analyze demand pattern changes</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Performance KPIs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Monitor key operational and financial metrics</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default JnaAnalyticsTab;
