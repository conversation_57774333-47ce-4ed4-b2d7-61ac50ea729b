
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { Settings, CheckCircle, Truck, Clock } from "lucide-react";

const JnaOpsTab = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Settings className="h-5 w-5" />
            JñaOps - Order Orchestration
          </h2>
          <p className="text-sm text-gray-600">End-to-end order management from dispatch to delivery confirmation</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5" />
              Dispatch Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Automated order dispatch and assignment</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Order Tracking
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Real-time order status and delivery updates</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Delivery Confirmation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Automated confirmation and customer notifications</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default JnaOpsTab;
