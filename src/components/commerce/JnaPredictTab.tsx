
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Brain, Target, MapPin, TrendingUp } from "lucide-react";

const JnaPredictTab = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Brain className="h-5 w-5" />
            JñaPredict - Demand Intelligence
          </h2>
          <p className="text-sm text-gray-600">AI-powered recommendations for best-selling items and hot zones</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Best Sellers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Identify trending products across all categories</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Hot Zones
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Area-based intelligence for demand concentration</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Data-driven suggestions for inventory and pricing</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default JnaPredictTab;
