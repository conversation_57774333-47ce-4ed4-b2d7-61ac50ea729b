
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Brain,
  Target,
  MapPin,
  TrendingUp,
  BarChart3,
  Users,
  Clock,
  Zap,
  AlertTriangle,
  Star,
  ArrowUp,
  ArrowDown,
  RefreshCw
} from "lucide-react";

const JnaPredictTab = () => {
  const [refreshing, setRefreshing] = useState(false);

  const bestSellers = [
    {
      rank: 1,
      name: "Milk - Full Cream 1L",
      sales: 234,
      growth: 12.5,
      revenue: 15210,
      trend: "up"
    },
    {
      rank: 2,
      name: "Bread - White Loaf",
      sales: 189,
      growth: 8.3,
      revenue: 4725,
      trend: "up"
    },
    {
      rank: 3,
      name: "Bananas - 1kg",
      sales: 156,
      growth: -2.1,
      revenue: 7020,
      trend: "down"
    },
    {
      rank: 4,
      name: "Rice - Basmati 1kg",
      sales: 143,
      growth: 15.7,
      revenue: 14300,
      trend: "up"
    }
  ];

  const hotZones = [
    {
      zone: "Zone 3",
      orders: 89,
      growth: 23.4,
      avgOrderValue: 245,
      peakHours: "12-2 PM, 7-9 PM",
      topCategory: "Groceries"
    },
    {
      zone: "Zone 1",
      orders: 76,
      growth: 18.2,
      avgOrderValue: 198,
      peakHours: "11-1 PM, 6-8 PM",
      topCategory: "Dairy"
    },
    {
      zone: "Zone 2",
      orders: 67,
      growth: 12.8,
      avgOrderValue: 167,
      peakHours: "1-3 PM, 8-10 PM",
      topCategory: "Snacks"
    }
  ];

  const predictions = [
    {
      type: "demand_spike",
      title: "Weekend Demand Surge Expected",
      description: "Groceries category likely to see 35% increase this weekend",
      confidence: 89,
      action: "Increase stock levels",
      impact: "High"
    },
    {
      type: "new_trend",
      title: "Organic Products Trending",
      description: "Organic milk and vegetables showing 45% growth trend",
      confidence: 76,
      action: "Expand organic range",
      impact: "Medium"
    },
    {
      type: "seasonal",
      title: "Monsoon Category Boost",
      description: "Tea, coffee, and snacks demand increasing with weather",
      confidence: 92,
      action: "Promote hot beverages",
      impact: "High"
    }
  ];

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 85) return 'bg-green-100 text-green-800';
    if (confidence >= 70) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getImpactColor = (impact: string) => {
    switch (impact.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 2000);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Business Analytics & Intelligence
          </h2>
          <p className="text-sm text-gray-600">AI-powered insights for demand forecasting and business optimization</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh Data
          </Button>
          <Button variant="outline">
            <BarChart3 className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Analytics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Prediction Accuracy
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">94.2%</div>
            <p className="text-xs text-muted-foreground">Last 30 days average</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Target className="h-4 w-4" />
              Active Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">23</div>
            <p className="text-xs text-muted-foreground">Actionable recommendations</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Revenue Impact
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">₹47K</div>
            <p className="text-xs text-muted-foreground">From AI recommendations</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4" />
              Customer Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,247</div>
            <p className="text-xs text-muted-foreground">Active customer profiles</p>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="bestsellers" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="bestsellers">Best Sellers</TabsTrigger>
          <TabsTrigger value="hotzones">Hot Zones</TabsTrigger>
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="bestsellers">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Top Performing Products
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {bestSellers.map((product) => (
                  <div key={product.rank} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-blue-600">#{product.rank}</span>
                      </div>
                      <div>
                        <h4 className="font-medium">{product.name}</h4>
                        <div className="text-sm text-gray-600">
                          {product.sales} units sold | ₹{product.revenue.toLocaleString()} revenue
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`flex items-center gap-1 ${product.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                        {product.trend === 'up' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                        <span className="font-medium">{Math.abs(product.growth)}%</span>
                      </div>
                      <div className="text-sm text-gray-600">vs last week</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="hotzones">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                High-Demand Zones
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {hotZones.map((zone, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-lg">{zone.zone}</h4>
                      <Badge className="bg-orange-100 text-orange-800">
                        +{zone.growth}% growth
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Orders Today: </span>
                        <span className="font-medium">{zone.orders}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Avg Order Value: </span>
                        <span className="font-medium">₹{zone.avgOrderValue}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Top Category: </span>
                        <span className="font-medium">{zone.topCategory}</span>
                      </div>
                    </div>

                    <div className="mt-3 p-2 bg-gray-50 rounded text-sm">
                      <span className="text-gray-600">Peak Hours: </span>
                      <span className="font-medium">{zone.peakHours}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="predictions">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                AI Predictions & Recommendations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {predictions.map((prediction, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-medium">{prediction.title}</h4>
                        <p className="text-sm text-gray-600 mt-1">{prediction.description}</p>
                      </div>
                      <div className="flex gap-2">
                        <Badge className={getConfidenceColor(prediction.confidence)}>
                          {prediction.confidence}% confidence
                        </Badge>
                        <Badge className={getImpactColor(prediction.impact)}>
                          {prediction.impact} impact
                        </Badge>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-sm">
                        <span className="text-gray-600">Recommended Action: </span>
                        <span className="font-medium">{prediction.action}</span>
                      </div>
                      <Button size="sm">
                        Take Action
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Market Trends & Patterns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-3">Category Performance</h4>
                    <div className="space-y-3">
                      {[
                        { category: "Groceries", growth: 23.4, color: "bg-green-500" },
                        { category: "Dairy", growth: 18.7, color: "bg-blue-500" },
                        { category: "Snacks", growth: 12.3, color: "bg-yellow-500" },
                        { category: "Beverages", growth: 8.9, color: "bg-purple-500" }
                      ].map((cat, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="text-sm">{cat.category}</span>
                          <div className="flex items-center gap-2">
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full ${cat.color}`}
                                style={{ width: `${(cat.growth / 25) * 100}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-medium">+{cat.growth}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-3">Time-based Patterns</h4>
                    <div className="space-y-3">
                      {[
                        { time: "Morning (6-10 AM)", orders: 45, peak: false },
                        { time: "Lunch (12-2 PM)", orders: 89, peak: true },
                        { time: "Evening (6-8 PM)", orders: 76, peak: true },
                        { time: "Night (8-11 PM)", orders: 34, peak: false }
                      ].map((slot, index) => (
                        <div key={index} className="flex items-center justify-between p-2 rounded">
                          <span className="text-sm">{slot.time}</span>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">{slot.orders} orders</span>
                            {slot.peak && <Badge variant="outline" className="text-xs">Peak</Badge>}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default JnaPredictTab;
