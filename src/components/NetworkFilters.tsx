
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON>lider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Filter, 
  RefreshCw, 
  TrendingUp, 
  Calendar,
  MapPin,
  BarChart3
} from "lucide-react";

const NetworkFilters = () => {
  return (
    <div className="space-y-4">
      {/* Scenario Info */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center justify-between">
            <span className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Active Scenario
            </span>
            <Badge variant="outline" className="text-xs">
              Version 2.1
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Scenario:</span>
              <span className="font-medium">Triad (Greenfield)</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Region:</span>
              <span className="font-medium">Southeast US</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Status:</span>
              <Badge variant="secondary" className="text-xs">
                Running
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Filters */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Quick Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Time Period */}
          <div>
            <label className="text-xs font-medium text-gray-700 mb-2 block">Time Period</label>
            <Select defaultValue="current">
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="current">Current Period</SelectItem>
                <SelectItem value="q1">Q1 2024</SelectItem>
                <SelectItem value="q2">Q2 2024</SelectItem>
                <SelectItem value="q3">Q3 2024</SelectItem>
                <SelectItem value="annual">Annual View</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Service Level */}
          <div>
            <label className="text-xs font-medium text-gray-700 mb-2 block">Service Level</label>
            <Select defaultValue="all">
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="express">Express</SelectItem>
                <SelectItem value="standard">Standard</SelectItem>
                <SelectItem value="economy">Economy</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Cost Threshold */}
          <div>
            <label className="text-xs font-medium text-gray-700 mb-3 block">
              Cost Threshold: $50K
            </label>
            <Slider
              defaultValue={[50]}
              max={200}
              step={10}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>$0K</span>
              <span>$200K</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Key Metrics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-600">Total Cost</span>
              <span className="text-sm font-semibold text-green-600">$2.4M</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-600">Service Level</span>
              <span className="text-sm font-semibold">94.2%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-600">Facilities</span>
              <span className="text-sm font-semibold">12 Active</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-600">Coverage</span>
              <span className="text-sm font-semibold">89.7%</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="space-y-2">
        <Button className="w-full" size="sm">
          <TrendingUp className="h-3 w-3 mr-2" />
          Run Optimization
        </Button>
        <Button variant="outline" className="w-full" size="sm">
          <Calendar className="h-3 w-3 mr-2" />
          Schedule Analysis
        </Button>
        <Button variant="outline" className="w-full" size="sm">
          <RefreshCw className="h-3 w-3 mr-2" />
          Reset Filters
        </Button>
      </div>
    </div>
  );
};

export default NetworkFilters;
