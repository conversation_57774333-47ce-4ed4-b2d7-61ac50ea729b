
import { Menu, Search, User, <PERSON>, Set<PERSON>s } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useLocation } from "react-router-dom";

interface HeaderProps {
  onMenuToggle: () => void;
}

const Header = ({ onMenuToggle }: HeaderProps) => {
  const location = useLocation();

  const getModuleInfo = () => {
    const path = location.pathname;
    if (path === '/') return { name: 'Route', description: 'COG Analysis' };
    if (path.startsWith('/dashboard')) return { name: 'Predict', description: 'AI Dashboard' };
    if (path.startsWith('/models')) return { name: 'Price', description: 'Optimization Models' };
    if (path.startsWith('/optimize')) return { name: 'Price', description: 'Optimization Engine' };
    if (path.startsWith('/reports')) return { name: 'Predict', description: 'Analytics & Reports' };
    if (path.startsWith('/commerce')) return { name: 'Commerce', description: 'E-commerce Platform' };
    if (path.startsWith('/build')) return { name: 'Build', description: 'Infrastructure Engineering' };
    if (path.startsWith('/demand') || path.startsWith('/period') || path.startsWith('/master-data')) return { name: 'Stock', description: 'Data Management' };
    if (path.startsWith('/suppliers') || path.startsWith('/plants') || path.startsWith('/warehouses') || path.startsWith('/transportation')) return { name: 'Route', description: 'Network Management' };
    if (path.startsWith('/lanes') || path.startsWith('/constraints')) return { name: 'Route', description: 'Constraints & Lanes' };
    if (path.startsWith('/settings')) return { name: 'Core', description: 'System Settings' };
    return { name: 'JñaOS', description: 'Supply Chain Platform' };
  };

  const moduleInfo = getModuleInfo();

  return (
    <header className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6 sticky top-0 z-40">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onMenuToggle}
          className="hover:bg-gray-100"
        >
          <Menu className="h-5 w-5" />
        </Button>
        
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">J</span>
          </div>
          <span className="font-semibold text-gray-900">JñaOS</span>
          {moduleInfo.name !== 'JñaOS' && (
            <>
              <span className="text-gray-400">|</span>
              <span className="font-medium text-blue-600">{moduleInfo.name}</span>
            </>
          )}
        </div>

        <div className="text-sm text-gray-500 hidden md:block">
          {moduleInfo.description}
        </div>
      </div>

      <div className="flex items-center gap-3">
        <div className="relative hidden md:block">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search..."
            className="pl-10 w-64 h-9"
          />
        </div>
        
        <Button variant="ghost" size="sm" className="hover:bg-gray-100">
          <Bell className="h-5 w-5" />
        </Button>
        
        <Button variant="ghost" size="sm" className="hover:bg-gray-100">
          <Settings className="h-5 w-5" />
        </Button>
        
        <div className="flex items-center gap-2 ml-2">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <User className="h-4 w-4 text-gray-600" />
          </div>
          <span className="text-sm font-medium hidden md:block">PB</span>
        </div>
      </div>
    </header>
  );
};

export default Header;
