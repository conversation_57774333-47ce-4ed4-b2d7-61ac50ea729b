
import { <PERSON>u, <PERSON>, User, <PERSON>, Set<PERSON><PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface HeaderProps {
  onMenuToggle: () => void;
}

const Header = ({ onMenuToggle }: HeaderProps) => {
  return (
    <header className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6 sticky top-0 z-40">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onMenuToggle}
          className="hover:bg-gray-100"
        >
          <Menu className="h-5 w-5" />
        </Button>
        
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">O</span>
          </div>
          <span className="font-semibold text-gray-900">Optiflow</span>
        </div>
        
        <div className="text-sm text-gray-500 hidden md:block">
          Projects / Models / COG
        </div>
      </div>

      <div className="flex items-center gap-3">
        <div className="relative hidden md:block">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search..."
            className="pl-10 w-64 h-9"
          />
        </div>
        
        <Button variant="ghost" size="sm" className="hover:bg-gray-100">
          <Bell className="h-5 w-5" />
        </Button>
        
        <Button variant="ghost" size="sm" className="hover:bg-gray-100">
          <Settings className="h-5 w-5" />
        </Button>
        
        <div className="flex items-center gap-2 ml-2">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <User className="h-4 w-4 text-gray-600" />
          </div>
          <span className="text-sm font-medium hidden md:block">Gaurav Mogra</span>
        </div>
      </div>
    </header>
  );
};

export default Header;
