
import { Card } from "@/components/ui/card";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";

const NetworkStats = () => {
  const stats = [
    {
      label: "Total Routes",
      value: "247",
      change: "+12",
      trend: "up",
      description: "Active transportation routes"
    },
    {
      label: "Avg Transit Time",
      value: "2.4h",
      change: "-0.3h",
      trend: "down",
      description: "Average delivery time"
    },
    {
      label: "Network Efficiency",
      value: "94.2%",
      change: "+2.1%",
      trend: "up",
      description: "Overall network performance"
    },
    {
      label: "Cost Per Mile",
      value: "$1.85",
      change: "±0",
      trend: "neutral",
      description: "Average transportation cost"
    }
  ];

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "down":
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Minus className="h-4 w-4 text-gray-400" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up":
        return "text-green-600";
      case "down":
        return "text-red-600";
      default:
        return "text-gray-400";
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index} className="p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
            <div className="flex items-center gap-1">
              {getTrendIcon(stat.trend)}
              <span className={`text-sm font-medium ${getTrendColor(stat.trend)}`}>
                {stat.change}
              </span>
            </div>
          </div>
          <div className="text-sm font-medium text-gray-700">{stat.label}</div>
          <div className="text-xs text-gray-500 mt-1">{stat.description}</div>
        </Card>
      ))}
    </div>
  );
};

export default NetworkStats;
