
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';
import { ModelData } from '@/types/model';
import { ModelLifecycleService } from '@/services/modelLifecycleService';

interface ModelDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode: 'create' | 'edit' | 'duplicate';
  model?: ModelData;
  onSave: (model: ModelData) => void;
}

const ModelDialog = ({ open, onOpenChange, mode, model, onSave }: ModelDialogProps) => {
  const [formData, setFormData] = useState({
    name: model?.name || '',
    type: model?.type || 'analysis' as const,
    description: model?.description || '',
    tags: model?.tags || [] as string[],
    isTemplate: model?.isTemplate || false,
  });

  const [newTag, setNewTag] = useState('');

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSave = async () => {
    try {
      let savedModel: ModelData;
      
      if (mode === 'create') {
        savedModel = await ModelLifecycleService.createModel(formData);
      } else if (mode === 'duplicate' && model) {
        savedModel = await ModelLifecycleService.duplicateModel({
          ...model,
          ...formData,
        });
      } else {
        // Edit mode - in a real app, this would update the existing model
        savedModel = { ...model!, ...formData, updatedAt: new Date().toISOString() };
      }
      
      onSave(savedModel);
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving model:', error);
    }
  };

  const getTitle = () => {
    switch (mode) {
      case 'create': return 'Create New Model';
      case 'edit': return 'Edit Model';
      case 'duplicate': return 'Duplicate Model';
      default: return 'Model';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Model Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter model name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Model Type</Label>
            <Select value={formData.type} onValueChange={(value: any) => setFormData(prev => ({ ...prev, type: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select model type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="optimization">Optimization</SelectItem>
                <SelectItem value="analysis">Analysis</SelectItem>
                <SelectItem value="prediction">Prediction</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Enter model description"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag"
                onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
              />
              <Button onClick={handleAddTag} variant="outline" size="sm">
                Add
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleRemoveTag(tag)}
                  />
                </Badge>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            {mode === 'create' ? 'Create' : mode === 'duplicate' ? 'Duplicate' : 'Save'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ModelDialog;
