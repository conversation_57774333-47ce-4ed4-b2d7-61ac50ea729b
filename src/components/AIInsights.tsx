
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  TrendingUp, 
  AlertTriangle, 
  Lightbulb, 
  Zap,
  Target,
  RefreshCw
} from "lucide-react";

const AIInsights = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [insights, setInsights] = useState([
    {
      id: 1,
      type: "optimization",
      title: "Route Optimization Opportunity",
      description: "AI detected a 15% cost reduction opportunity by consolidating shipments from Dallas and Houston warehouses.",
      impact: "High",
      confidence: 92,
      icon: Target,
      action: "Optimize Routes"
    },
    {
      id: 2,
      type: "prediction",
      title: "Demand Surge Prediction",
      description: "Machine learning models predict a 23% increase in demand for West Coast regions next month.",
      impact: "Medium",
      confidence: 87,
      icon: TrendingUp,
      action: "Adjust Inventory"
    },
    {
      id: 3,
      type: "alert",
      title: "Potential Bottleneck Alert",
      description: "Chicago distribution center showing signs of capacity constraints based on historical patterns.",
      impact: "High",
      confidence: 78,
      icon: AlertTriangle,
      action: "Review Capacity"
    }
  ]);

  const generateNewInsights = async () => {
    setIsGenerating(true);
    // Simulate AI processing
    setTimeout(() => {
      const newInsight = {
        id: insights.length + 1,
        type: "recommendation",
        title: "Cross-Docking Opportunity",
        description: "AI recommends implementing cross-docking at Memphis hub to reduce handling time by 18%.",
        impact: "Medium",
        confidence: 85,
        icon: Lightbulb,
        action: "Implement"
      };
      setInsights(prev => [newInsight, ...prev.slice(0, 2)]);
      setIsGenerating(false);
    }, 2000);
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case "High": return "bg-red-100 text-red-800";
      case "Medium": return "bg-yellow-100 text-yellow-800";
      case "Low": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "optimization": return Target;
      case "prediction": return TrendingUp;
      case "alert": return AlertTriangle;
      case "recommendation": return Lightbulb;
      default: return Brain;
    }
  };

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Brain className="h-5 w-5 text-purple-600" />
          AI Insights & Recommendations
        </CardTitle>
        <Button
          variant="outline"
          size="sm"
          onClick={generateNewInsights}
          disabled={isGenerating}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${isGenerating ? 'animate-spin' : ''}`} />
          {isGenerating ? 'Analyzing...' : 'Generate'}
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {insights.map((insight) => {
          const Icon = getTypeIcon(insight.type);
          return (
            <div key={insight.id} className="p-4 border rounded-lg bg-gradient-to-r from-blue-50 to-purple-50">
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Icon className="h-4 w-4 text-blue-600" />
                  <h4 className="font-medium text-sm">{insight.title}</h4>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={getImpactColor(insight.impact)}>
                    {insight.impact}
                  </Badge>
                  <span className="text-xs text-gray-500">{insight.confidence}% confident</span>
                </div>
              </div>
              <p className="text-sm text-gray-600 mb-3">{insight.description}</p>
              <Button variant="outline" size="sm" className="w-full">
                <Zap className="h-3 w-3 mr-1" />
                {insight.action}
              </Button>
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
};

export default AIInsights;
