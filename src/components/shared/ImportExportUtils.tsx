
import { Button } from "@/components/ui/button";
import { Download, Upload } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ImportExportUtilsProps {
  data: any[];
  filename: string;
  onImport?: (data: any[]) => void;
}

const ImportExportUtils = ({ data, filename, onImport }: ImportExportUtilsProps) => {
  const { toast } = useToast();

  const handleExport = () => {
    try {
      const dataStr = JSON.stringify(data, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
      
      const exportFileDefaultName = `${filename}-${new Date().toISOString().split('T')[0]}.json`;
      
      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
      
      toast({
        title: "Export Successful",
        description: `${filename} data has been exported successfully.`,
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "There was an error exporting the data.",
        variant: "destructive",
      });
    }
  };

  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json,.csv';
    
    input.onchange = (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const content = e.target?.result as string;
            let importedData;
            
            if (file.name.endsWith('.json')) {
              importedData = JSON.parse(content);
            } else if (file.name.endsWith('.csv')) {
              // Basic CSV parsing - in production, you'd want a proper CSV parser
              const lines = content.split('\n');
              const headers = lines[0].split(',');
              importedData = lines.slice(1).map(line => {
                const values = line.split(',');
                const obj: any = {};
                headers.forEach((header, index) => {
                  obj[header.trim()] = values[index]?.trim();
                });
                return obj;
              }).filter(obj => Object.values(obj).some(value => value));
            }
            
            if (onImport && importedData) {
              onImport(importedData);
              toast({
                title: "Import Successful",
                description: `${importedData.length} records imported successfully.`,
              });
            }
          } catch (error) {
            toast({
              title: "Import Failed",
              description: "There was an error parsing the imported file.",
              variant: "destructive",
            });
          }
        };
        reader.readAsText(file);
      }
    };
    
    input.click();
  };

  return (
    <>
      <Button variant="outline" size="sm" onClick={handleImport}>
        <Upload className="h-4 w-4 mr-2" />
        Import
      </Button>
      <Button variant="outline" size="sm" onClick={handleExport}>
        <Download className="h-4 w-4 mr-2" />
        Export
      </Button>
    </>
  );
};

export default ImportExportUtils;
