
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  MessageCircle, 
  Send, 
  Bot, 
  User, 
  Minimize2,
  Maximize2
} from "lucide-react";

interface Message {
  id: string;
  type: "user" | "ai";
  content: string;
  timestamp: string;
  suggestions?: string[];
}

const AIChatAssistant = () => {
  const [isMinimized, setIsMinimized] = useState(false);
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      type: "ai",
      content: "Hello! I'm your AI logistics assistant. I can help you with route optimization, demand forecasting, inventory analysis, and more. What would you like to know?",
      timestamp: "Just now",
      suggestions: ["Optimize routes", "Check inventory", "Analyze demand", "Review KPIs"]
    }
  ]);

  const sendMessage = () => {
    if (!message.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: message,
      timestamp: "Just now"
    };

    setMessages(prev => [...prev, userMessage]);
    setMessage("");

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content: generateAIResponse(message),
        timestamp: "Just now",
        suggestions: ["Tell me more", "Show analysis", "Export data", "Schedule optimization"]
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  const generateAIResponse = (userInput: string) => {
    const responses = [
      "Based on your current logistics data, I've identified several optimization opportunities. Your Dallas-Houston corridor shows 23% efficiency gains possible through route consolidation.",
      "I've analyzed your inventory levels and predict a potential stockout in the West Coast region next week. Would you like me to suggest rebalancing strategies?",
      "Your transportation costs have increased 12% this quarter. I recommend reviewing carrier contracts and implementing AI-driven load optimization.",
      "I notice unusual demand patterns in your Chicago distribution center. This could indicate a supply chain disruption. Shall I investigate further?"
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const handleSuggestionClick = (suggestion: string) => {
    setMessage(suggestion);
  };

  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsMinimized(false)}
          className="rounded-full w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
        >
          <MessageCircle className="h-5 w-5" />
        </Button>
      </div>
    );
  }

  return (
    <Card className="fixed bottom-4 right-4 w-80 h-96 z-50 shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <CardTitle className="text-sm font-semibold flex items-center gap-2">
          <Bot className="h-4 w-4" />
          AI Assistant
        </CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsMinimized(true)}
          className="text-white hover:bg-white/20 h-6 w-6 p-0"
        >
          <Minimize2 className="h-3 w-3" />
        </Button>
      </CardHeader>
      <CardContent className="p-0 flex flex-col h-full">
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-3 space-y-3">
          {messages.map((msg) => (
            <div key={msg.id} className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[80%] p-2 rounded-lg ${
                msg.type === 'user' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-100 text-gray-800'
              }`}>
                <div className="flex items-start gap-2">
                  {msg.type === 'ai' && <Bot className="h-3 w-3 mt-0.5 flex-shrink-0" />}
                  {msg.type === 'user' && <User className="h-3 w-3 mt-0.5 flex-shrink-0" />}
                  <div className="text-xs">{msg.content}</div>
                </div>
                {msg.suggestions && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {msg.suggestions.map((suggestion, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="text-xs cursor-pointer hover:bg-blue-50"
                        onClick={() => handleSuggestionClick(suggestion)}
                      >
                        {suggestion}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Input */}
        <div className="p-3 border-t">
          <div className="flex gap-2">
            <Input
              placeholder="Ask about your logistics..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
              className="text-sm"
            />
            <Button onClick={sendMessage} size="sm">
              <Send className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AIChatAssistant;
