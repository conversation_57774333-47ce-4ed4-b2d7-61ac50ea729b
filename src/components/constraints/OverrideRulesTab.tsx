
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Edit, Settings, AlertTriangle, CheckCircle, Clock, Shield } from "lucide-react";
import { OverrideRule } from "@/types/laneConstraint";
import { getOverrideTypeColor, getPriorityColor } from "@/utils/laneConstraintUtils";
import { getStatusColor } from "@/utils/constraintUtils";
import MetricsCard from "./MetricsCard";
import SearchAndFilter from "./SearchAndFilter";

interface OverrideRulesTabProps {
  overrideRules: OverrideRule[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

const OverrideRulesTab = ({ overrideRules, searchTerm, setSearchTerm }: OverrideRulesTabProps) => {
  const filteredRules = overrideRules.filter(rule =>
    rule.ruleName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.overrideType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <MetricsCard
          icon={Settings}
          iconColor="text-blue-500"
          label="Total Rules"
          value={overrideRules.length}
        />
        <MetricsCard
          icon={CheckCircle}
          iconColor="text-green-500"
          label="Active Rules"
          value={overrideRules.filter(r => r.isActive).length}
        />
        <MetricsCard
          icon={AlertTriangle}
          iconColor="text-red-500"
          label="High Priority"
          value={overrideRules.filter(r => r.priority >= 8).length}
        />
        <MetricsCard
          icon={Shield}
          iconColor="text-purple-500"
          label="Emergency Rules"
          value={overrideRules.filter(r => r.overrideType === "Emergency Override").length}
        />
      </div>

      <SearchAndFilter
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        placeholder="Search override rules by name, type, or description..."
        showAddButton={true}
        addButtonText="Add Override Rule"
        onAddClick={() => console.log("Add override rule clicked")}
      />

      <Card>
        <CardHeader>
          <CardTitle>Override Rules ({filteredRules.length})</CardTitle>
          <CardDescription>
            Manage lane-specific constraint overrides and exceptions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Rule Name</TableHead>
                <TableHead>Override Type</TableHead>
                <TableHead>Conditions</TableHead>
                <TableHead>Target Value</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Applied Lanes</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Modified</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRules.map((rule) => (
                <TableRow key={rule.id}>
                  <TableCell>
                    <div className="font-medium">{rule.ruleName}</div>
                    <div className="text-sm text-gray-500">{rule.description}</div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getOverrideTypeColor(rule.overrideType)}>
                      {rule.overrideType}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm font-mono max-w-xs truncate" title={rule.conditions}>
                      {rule.conditions}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-mono text-sm text-blue-600">{rule.targetValue}</span>
                  </TableCell>
                  <TableCell>
                    <Badge className={getPriorityColor(rule.priority)}>
                      {rule.priority}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {rule.appliedLanes.length} lane{rule.appliedLanes.length !== 1 ? 's' : ''}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(rule.isActive ? "Active" : "Inactive")}>
                      {rule.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>{rule.lastModified}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Settings className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </>
  );
};

export default OverrideRulesTab;
