
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Edit, Settings, CheckCircle, AlertTriangle, Clock } from "lucide-react";
import { Constraint } from "@/types/constraint";
import { getSeverityColor, getStatusColor } from "@/utils/constraintUtils";
import MetricsCard from "./MetricsCard";
import SearchAndFilter from "./SearchAndFilter";

interface ConstraintsTabProps {
  constraints: Constraint[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

const ConstraintsTab = ({ constraints, searchTerm, setSearchTerm }: ConstraintsTabProps) => {
  const filteredConstraints = constraints.filter(constraint =>
    constraint.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    constraint.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    constraint.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <MetricsCard
          icon={Settings}
          iconColor="text-blue-500"
          label="Total Constraints"
          value={constraints.length}
        />
        <MetricsCard
          icon={CheckCircle}
          iconColor="text-green-500"
          label="Active"
          value={constraints.filter(c => c.status === "Active").length}
        />
        <MetricsCard
          icon={AlertTriangle}
          iconColor="text-red-500"
          label="Critical Priority"
          value={constraints.filter(c => c.priority === "Critical").length}
        />
        <MetricsCard
          icon={Clock}
          iconColor="text-yellow-500"
          label="Time Constraints"
          value={constraints.filter(c => c.type.includes("Time")).length}
        />
      </div>

      <SearchAndFilter
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        placeholder="Search constraints by name, type, or category..."
        showAddButton={true}
        addButtonText="Add Constraint"
        onAddClick={() => console.log("Add constraint clicked")}
      />

      <Card>
        <CardHeader>
          <CardTitle>Constraints ({filteredConstraints.length})</CardTitle>
          <CardDescription>
            Manage and configure operational constraints
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Applied To</TableHead>
                <TableHead>Last Modified</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredConstraints.map((constraint) => (
                <TableRow key={constraint.id}>
                  <TableCell>
                    <div className="font-medium">{constraint.name}</div>
                    <div className="text-sm text-gray-500">{constraint.description}</div>
                  </TableCell>
                  <TableCell>{constraint.type}</TableCell>
                  <TableCell>{constraint.category}</TableCell>
                  <TableCell>
                    <Badge className={getSeverityColor(constraint.priority)}>
                      {constraint.priority}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(constraint.status)}>
                      {constraint.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{constraint.appliedTo}</TableCell>
                  <TableCell>{constraint.lastModified}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Settings className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </>
  );
};

export default ConstraintsTab;
