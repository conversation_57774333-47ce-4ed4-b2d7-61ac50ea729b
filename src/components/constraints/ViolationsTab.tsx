
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Edit, FileText, XCircle, AlertTriangle, Clock, CheckCircle } from "lucide-react";
import { Violation } from "@/types/constraint";
import { getSeverityColor, getStatusColor } from "@/utils/constraintUtils";
import MetricsCard from "./MetricsCard";
import SearchAndFilter from "./SearchAndFilter";

interface ViolationsTabProps {
  violations: Violation[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

const ViolationsTab = ({ violations, searchTerm, setSearchTerm }: ViolationsTabProps) => {
  const filteredViolations = violations.filter(violation =>
    violation.constraintName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    violation.driver.toLowerCase().includes(searchTerm.toLowerCase()) ||
    violation.vehicle.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <MetricsCard
          icon={XCircle}
          iconColor="text-red-500"
          label="Total Violations"
          value={violations.length}
        />
        <MetricsCard
          icon={AlertTriangle}
          iconColor="text-red-500"
          label="Critical"
          value={violations.filter(v => v.severity === "Critical").length}
        />
        <MetricsCard
          icon={Clock}
          iconColor="text-yellow-500"
          label="Under Review"
          value={violations.filter(v => v.status === "Under Review").length}
        />
        <MetricsCard
          icon={CheckCircle}
          iconColor="text-green-500"
          label="Resolved"
          value={violations.filter(v => v.status === "Resolved").length}
        />
      </div>

      <SearchAndFilter
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        placeholder="Search violations by constraint, driver, or vehicle..."
      />

      <Card>
        <CardHeader>
          <CardTitle>Constraint Violations ({filteredViolations.length})</CardTitle>
          <CardDescription>
            Monitor and track constraint violations across operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Constraint</TableHead>
                <TableHead>Violation Type</TableHead>
                <TableHead>Severity</TableHead>
                <TableHead>Driver/Vehicle</TableHead>
                <TableHead>Route</TableHead>
                <TableHead>Timestamp</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredViolations.map((violation) => (
                <TableRow key={violation.id}>
                  <TableCell>
                    <div className="font-medium">{violation.constraintName}</div>
                    <div className="text-sm text-gray-500">{violation.description}</div>
                  </TableCell>
                  <TableCell>{violation.violationType}</TableCell>
                  <TableCell>
                    <Badge className={getSeverityColor(violation.severity)}>
                      {violation.severity}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">{violation.driver}</div>
                      <div className="text-sm text-gray-500">{violation.vehicle}</div>
                    </div>
                  </TableCell>
                  <TableCell>{violation.route}</TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">{violation.timestamp}</div>
                      {violation.duration !== "N/A" && (
                        <div className="text-sm text-gray-500">{violation.duration}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(violation.status)}>
                      {violation.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <FileText className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </>
  );
};

export default ViolationsTab;
