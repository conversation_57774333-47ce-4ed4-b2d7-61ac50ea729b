
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Search, Plus } from "lucide-react";

interface SearchAndFilterProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  placeholder: string;
  showAddButton?: boolean;
  addButtonText?: string;
  onAddClick?: () => void;
}

const SearchAndFilter = ({ 
  searchTerm, 
  setSearchTerm, 
  placeholder, 
  showAddButton = false,
  addButtonText = "Add",
  onAddClick
}: SearchAndFilterProps) => {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 flex-1">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder={placeholder}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              Filter
            </Button>
          </div>
          {showAddButton && (
            <Button className="ml-4" onClick={onAddClick}>
              <Plus className="h-4 w-4 mr-2" />
              {addButtonText}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SearchAndFilter;
