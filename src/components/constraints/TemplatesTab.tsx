
import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Edit, Copy, Download, Star, FileText, Users, CheckCircle } from "lucide-react";
import { ConstraintTemplate } from "@/types/laneConstraint";
import { getTemplateTypeColor } from "@/utils/laneConstraintUtils";
import MetricsCard from "./MetricsCard";
import SearchAndFilter from "./SearchAndFilter";

interface TemplatesTabProps {
  templates: ConstraintTemplate[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

const TemplatesTab = ({ templates, searchTerm, setSearchTerm }: TemplatesTabProps) => {
  const filteredTemplates = templates.filter(template =>
    template.templateName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <MetricsCard
          icon={FileText}
          iconColor="text-blue-500"
          label="Total Templates"
          value={templates.length}
        />
        <MetricsCard
          icon={Star}
          iconColor="text-yellow-500"
          label="Default Templates"
          value={templates.filter(t => t.isDefault).length}
        />
        <MetricsCard
          icon={Users}
          iconColor="text-green-500"
          label="Total Usage"
          value={templates.reduce((acc, t) => acc + t.usageCount, 0)}
        />
        <MetricsCard
          icon={CheckCircle}
          iconColor="text-purple-500"
          label="Categories"
          value={new Set(templates.map(t => t.category)).size}
        />
      </div>

      <SearchAndFilter
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        placeholder="Search templates by name, category, or description..."
        showAddButton={true}
        addButtonText="Create Template"
        onAddClick={() => console.log("Create template clicked")}
      />

      <Card>
        <CardHeader>
          <CardTitle>Constraint Templates ({filteredTemplates.length})</CardTitle>
          <CardDescription>
            Predefined constraint sets for different lane types
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Template Name</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Constraints</TableHead>
                <TableHead>Applicable Lane Types</TableHead>
                <TableHead>Usage Count</TableHead>
                <TableHead>Default</TableHead>
                <TableHead>Created By</TableHead>
                <TableHead>Last Modified</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTemplates.map((template) => (
                <TableRow key={template.id}>
                  <TableCell>
                    <div className="font-medium">{template.templateName}</div>
                    <div className="text-sm text-gray-500">{template.description}</div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getTemplateTypeColor(template.category)}>
                      {template.category}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {template.constraints.length} constraint{template.constraints.length !== 1 ? 's' : ''}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {template.applicableLaneTypes.join(", ")}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">{template.usageCount}</span>
                  </TableCell>
                  <TableCell>
                    {template.isDefault && (
                      <Badge className="bg-yellow-100 text-yellow-800">
                        <Star className="h-3 w-3 mr-1" />
                        Default
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>{template.createdBy}</TableCell>
                  <TableCell>{template.lastModified}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </>
  );
};

export default TemplatesTab;
