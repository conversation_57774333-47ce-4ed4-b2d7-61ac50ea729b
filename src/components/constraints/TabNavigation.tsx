
import { Settings, AlertTriangle, Shield } from "lucide-react";

interface TabNavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const TabNavigation = ({ activeTab, setActiveTab }: TabNavigationProps) => {
  const tabs = [
    { id: "constraints", label: "Constraints", icon: Settings },
    { id: "violations", label: "Violations", icon: AlertTriangle },
    { id: "compliance", label: "Compliance", icon: Shield },
  ];

  return (
    <div className="border-b border-gray-200">
      <nav className="-mb-px flex space-x-8">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <div className="flex items-center gap-2">
                <Icon className="h-4 w-4" />
                {tab.label}
              </div>
            </button>
          );
        })}
      </nav>
    </div>
  );
};

export default TabNavigation;
