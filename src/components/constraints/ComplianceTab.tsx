
import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { ComplianceMetric } from "@/types/constraint";
import MetricsCard from "./MetricsCard";
import { Shield } from "lucide-react";

interface ComplianceTabProps {
  complianceMetrics: ComplianceMetric[];
}

const ComplianceTab = ({ complianceMetrics }: ComplianceTabProps) => {
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {complianceMetrics.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{metric.category}</p>
                  <p className="text-2xl font-bold">{metric.complianceRate}%</p>
                  <p className="text-xs text-gray-500">{metric.violations} violations / {metric.totalChecks} checks</p>
                </div>
                <div className={`text-sm font-medium ${
                  metric.trend === "up" ? "text-green-600" : 
                  metric.trend === "down" ? "text-red-600" : "text-gray-600"
                }`}>
                  {metric.trend === "up" ? "↗" : metric.trend === "down" ? "↘" : "→"}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Compliance Overview</CardTitle>
          <CardDescription>
            Monitor compliance rates across different constraint categories
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {complianceMetrics.map((metric, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{metric.category}</h4>
                    <span className="text-lg font-bold">{metric.complianceRate}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        metric.complianceRate >= 95 ? "bg-green-500" :
                        metric.complianceRate >= 90 ? "bg-yellow-500" : "bg-red-500"
                      }`}
                      style={{ width: `${metric.complianceRate}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-sm text-gray-500 mt-1">
                    <span>{metric.violations} violations</span>
                    <span>{metric.totalChecks} total checks</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default ComplianceTab;
