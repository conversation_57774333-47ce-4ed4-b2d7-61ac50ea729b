
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Edit, Trash2, Route } from "lucide-react";
import { LaneConstraintMapping } from "@/types/laneConstraint";
import SearchAndFilter from "./SearchAndFilter";

interface LaneMappingsTabProps {
  mappings: LaneConstraintMapping[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  onDelete: (mappingId: number) => void;
}

const LaneMappingsTab = ({ mappings, searchTerm, setSearchTerm, onDelete }: LaneMappingsTabProps) => {
  const getConstraintTypeColor = (type: string) => {
    switch (type) {
      case "Capacity": return "bg-blue-100 text-blue-800";
      case "Time": return "bg-green-100 text-green-800";
      case "Route": return "bg-purple-100 text-purple-800";
      case "Physical": return "bg-orange-100 text-orange-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const filteredMappings = mappings.filter(mapping =>
    mapping.laneName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    mapping.constraintName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    mapping.constraintType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      <SearchAndFilter
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        placeholder="Search by lane name or constraint..."
        showAddButton={true}
        addButtonText="Map Constraint"
        onAddClick={() => console.log("Map constraint clicked")}
      />

      <Card>
        <CardHeader>
          <CardTitle>Lane-Constraint Mappings ({filteredMappings.length})</CardTitle>
          <CardDescription>
            Manage how constraints are applied to specific transportation lanes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Lane</TableHead>
                <TableHead>Constraint</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Default Value</TableHead>
                <TableHead>Override Value</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Modified</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMappings.map((mapping) => (
                <TableRow key={mapping.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Route className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">{mapping.laneName}</span>
                    </div>
                  </TableCell>
                  <TableCell>{mapping.constraintName}</TableCell>
                  <TableCell>
                    <Badge className={getConstraintTypeColor(mapping.constraintType)}>
                      {mapping.constraintType}
                    </Badge>
                  </TableCell>
                  <TableCell className="font-mono text-sm">{mapping.value}</TableCell>
                  <TableCell>
                    {mapping.overrideValue ? (
                      <span className="font-mono text-sm text-blue-600">{mapping.overrideValue}</span>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge className={mapping.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
                      {mapping.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>{mapping.lastModified}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => onDelete(mapping.id)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </>
  );
};

export default LaneMappingsTab;
