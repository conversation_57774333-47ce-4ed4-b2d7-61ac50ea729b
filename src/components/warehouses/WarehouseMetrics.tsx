
import { Card, CardContent } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";

interface WarehouseMetricsProps {
  icon: LucideIcon;
  iconColor: string;
  label: string;
  value: string | number;
  change?: {
    value: number;
    isPositive: boolean;
  };
}

const WarehouseMetrics = ({ 
  icon: Icon, 
  iconColor, 
  label, 
  value, 
  change 
}: WarehouseMetricsProps) => {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Icon className={`h-5 w-5 ${iconColor}`} />
            <div>
              <p className="text-sm text-gray-600">{label}</p>
              <p className="text-2xl font-bold">{value}</p>
            </div>
          </div>
          {change && (
            <div className={`text-sm font-medium ${
              change.isPositive ? 'text-green-600' : 'text-red-600'
            }`}>
              {change.isPositive ? '+' : ''}{change.value}%
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default WarehouseMetrics;
