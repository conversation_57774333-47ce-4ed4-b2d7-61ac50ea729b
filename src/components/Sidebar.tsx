
import { useState } from "react";
import { cn } from "@/lib/utils";
import { menuSections } from "./sidebar/menuData";
import { SidebarProps } from "./sidebar/types";
import SidebarHeader from "./sidebar/SidebarHeader";
import SidebarFooter from "./sidebar/SidebarFooter";
import SidebarSection from "./sidebar/SidebarSection";

const Sidebar = ({ isCollapsed }: SidebarProps) => {
  const [expandedSections, setExpandedSections] = useState<string[]>(["Analysis"]);

  const toggleSection = (title: string) => {
    if (isCollapsed) return;
    setExpandedSections(prev => 
      prev.includes(title) 
        ? prev.filter(section => section !== title)
        : [...prev, title]
    );
  };

  return (
    <aside className={cn(
      "bg-gray-900 text-white transition-all duration-300 ease-in-out flex flex-col h-screen border-r border-gray-800",
      isCollapsed ? "w-16" : "w-64"
    )}>
      <SidebarHeader isCollapsed={isCollapsed} />

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto py-4">
        <nav className="space-y-6 px-2">
          {menuSections.map((section) => {
            const isSectionExpanded = expandedSections.includes(section.title);
            
            return (
              <SidebarSection
                key={section.title}
                section={section}
                isCollapsed={isCollapsed}
                isExpanded={isSectionExpanded}
                onToggleSection={toggleSection}
              />
            );
          })}
        </nav>
      </div>

      <SidebarFooter isCollapsed={isCollapsed} />
    </aside>
  );
};

export default Sidebar;
