
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { LucideIcon } from "lucide-react";

interface AnalyticsMetricsProps {
  icon: LucideIcon;
  iconColor: string;
  title: string;
  value: string | number;
  description: string;
  trend?: {
    value: number;
    label: string;
    isPositive: boolean;
  };
  badge?: {
    text: string;
    variant: "default" | "secondary" | "destructive" | "outline";
  };
}

const AnalyticsMetrics = ({ 
  icon: Icon, 
  iconColor, 
  title, 
  value, 
  description,
  trend,
  badge
}: AnalyticsMetricsProps) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Icon className={`h-4 w-4 ${iconColor}`} />
          {title}
        </CardTitle>
        {badge && <Badge variant={badge.variant}>{badge.text}</Badge>}
      </Card<PERSON>eader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <CardDescription className="text-xs text-gray-600 mt-1">
          {description}
        </CardDescription>
        {trend && (
          <div className={`flex items-center gap-1 mt-2 text-xs ${
            trend.isPositive ? 'text-green-600' : 'text-red-600'
          }`}>
            <span>{trend.isPositive ? '↗' : '↘'}</span>
            <span>{trend.value}% {trend.label}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AnalyticsMetrics;
