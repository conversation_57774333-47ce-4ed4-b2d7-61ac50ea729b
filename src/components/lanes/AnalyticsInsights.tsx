
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Brain, TrendingUp, AlertTriangle, Lightbulb, Target } from "lucide-react";

const AnalyticsInsights = () => {
  const insights = [
    {
      id: 1,
      type: "optimization",
      icon: Target,
      title: "Route Consolidation Opportunity",
      description: "Consolidating Chicago-Detroit and Chicago-Milwaukee routes could reduce costs by 18%",
      impact: "High",
      confidence: 89,
      action: "Review Routes"
    },
    {
      id: 2,
      type: "prediction",
      icon: TrendingUp,
      title: "Seasonal Demand Pattern",
      description: "Q4 demand typically increases by 35% - prepare capacity adjustments",
      impact: "Medium",
      confidence: 92,
      action: "Plan Capacity"
    },
    {
      id: 3,
      type: "alert",
      icon: AlertTriangle,
      title: "Utilization Bottleneck",
      description: "Northeast route approaching maximum capacity during peak hours",
      impact: "High",
      confidence: 76,
      action: "Add Capacity"
    }
  ];

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case "High": return "bg-red-100 text-red-800";
      case "Medium": return "bg-yellow-100 text-yellow-800";
      case "Low": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-purple-600" />
          AI-Powered Insights
        </CardTitle>
        <CardDescription>
          Machine learning recommendations based on historical data and patterns
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {insights.map((insight) => {
          const Icon = insight.icon;
          return (
            <div key={insight.id} className="p-4 border rounded-lg bg-gradient-to-r from-blue-50 to-purple-50">
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Icon className="h-4 w-4 text-blue-600" />
                  <h4 className="font-medium text-sm">{insight.title}</h4>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getImpactColor(insight.impact)}>
                    {insight.impact}
                  </Badge>
                  <span className="text-xs text-gray-500">{insight.confidence}%</span>
                </div>
              </div>
              <p className="text-sm text-gray-600 mb-3">{insight.description}</p>
              <Button variant="outline" size="sm" className="w-full">
                {insight.action}
              </Button>
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
};

export default AnalyticsInsights;
