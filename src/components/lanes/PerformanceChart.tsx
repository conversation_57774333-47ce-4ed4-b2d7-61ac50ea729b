
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Line<PERSON>hart, Line, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON>, <PERSON> } from "recharts";

interface PerformanceChartProps {
  title: string;
  description: string;
  data: any[];
  type: "line" | "bar";
  dataKey: string;
  xAxisKey: string;
  color?: string;
}

const PerformanceChart = ({ 
  title, 
  description, 
  data, 
  type, 
  dataKey, 
  xAxisKey, 
  color = "#3b82f6" 
}: PerformanceChartProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            {type === "line" ? (
              <LineChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey={xA<PERSON>s<PERSON><PERSON>} />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey={dataKey} 
                  stroke={color} 
                  strokeWidth={2}
                  dot={{ fill: color, strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            ) : (
              <BarChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey={xAxisKey} />
                <YAxis />
                <Tooltip />
                <Bar dataKey={dataKey} fill={color} />
              </BarChart>
            )}
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};

export default PerformanceChart;
