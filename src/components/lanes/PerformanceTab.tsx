
import { TrendingUp, Clock, DollarSign, Truck, Target, AlertTriangle } from "lucide-react";
import PerformanceMetrics from "./PerformanceMetrics";
import PerformanceChart from "./PerformanceChart";

const PerformanceTab = () => {
  const performanceData = [
    { month: 'Jan', utilization: 78, cost: 45000, onTime: 94 },
    { month: 'Feb', utilization: 82, cost: 48000, onTime: 91 },
    { month: 'Mar', utilization: 85, cost: 52000, onTime: 96 },
    { month: 'Apr', utilization: 88, cost: 49000, onTime: 93 },
    { month: 'May', utilization: 92, cost: 51000, onTime: 97 },
    { month: 'Jun', utilization: 89, cost: 47000, onTime: 95 }
  ];

  const lanePerformance = [
    { lane: 'Chicago-Detroit', performance: 92 },
    { lane: 'West Coast', performance: 88 },
    { lane: 'Northeast', performance: 76 },
    { lane: 'Texas Triangle', performance: 85 }
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <PerformanceMetrics
          icon={TrendingUp}
          iconColor="text-green-500"
          label="Avg Utilization"
          value="85%"
          change={{ value: 5.2, isPositive: true }}
        />
        <PerformanceMetrics
          icon={Clock}
          iconColor="text-blue-500"
          label="On-Time Performance"
          value="94.5%"
          change={{ value: 2.1, isPositive: true }}
        />
        <PerformanceMetrics
          icon={DollarSign}
          iconColor="text-purple-500"
          label="Cost Efficiency"
          value="$1.85/mile"
          change={{ value: 3.8, isPositive: false }}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PerformanceChart
          title="Utilization Trend"
          description="Lane utilization percentage over time"
          data={performanceData}
          type="line"
          dataKey="utilization"
          xAxisKey="month"
          color="#10b981"
        />
        
        <PerformanceChart
          title="Transportation Costs"
          description="Monthly transportation costs"
          data={performanceData}
          type="bar"
          dataKey="cost"
          xAxisKey="month"
          color="#8b5cf6"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PerformanceChart
          title="On-Time Delivery"
          description="On-time delivery percentage by month"
          data={performanceData}
          type="line"
          dataKey="onTime"
          xAxisKey="month"
          color="#3b82f6"
        />
        
        <PerformanceChart
          title="Lane Performance Comparison"
          description="Performance score by lane"
          data={lanePerformance}
          type="bar"
          dataKey="performance"
          xAxisKey="lane"
          color="#f59e0b"
        />
      </div>
    </div>
  );
};

export default PerformanceTab;
