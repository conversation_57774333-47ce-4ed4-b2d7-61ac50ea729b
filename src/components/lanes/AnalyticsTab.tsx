
import { BarChart3, <PERSON><PERSON><PERSON>, Activity, Zap, Brain, TrendingUp } from "lucide-react";
import AnalyticsMetrics from "./AnalyticsMetrics";
import AnalyticsInsights from "./AnalyticsInsights";
import PerformanceChart from "./PerformanceChart";

const AnalyticsTab = () => {
  const demandForecast = [
    { week: 'Week 1', predicted: 120, actual: 118 },
    { week: 'Week 2', predicted: 135, actual: 142 },
    { week: 'Week 3', predicted: 128, actual: 125 },
    { week: 'Week 4', predicted: 155, actual: 151 },
    { week: 'Week 5', predicted: 148, actual: null },
    { week: 'Week 6', predicted: 162, actual: null }
  ];

  const efficiencyData = [
    { lane: 'Chicago-Detroit', efficiency: 94 },
    { lane: 'West Coast', efficiency: 87 },
    { lane: 'Northeast', efficiency: 79 },
    { lane: 'Texas Triangle', efficiency: 91 }
  ];

  const costAnalysis = [
    { category: 'Fuel', amount: 35000, percentage: 45 },
    { category: 'Labor', amount: 28000, percentage: 36 },
    { category: 'Maintenance', amount: 8000, percentage: 10 },
    { category: 'Insurance', amount: 4500, percentage: 6 },
    { category: 'Other', amount: 2500, percentage: 3 }
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <AnalyticsMetrics
          icon={BarChart3}
          iconColor="text-blue-500"
          title="Forecast Accuracy"
          value="94.2%"
          description="ML prediction accuracy"
          trend={{ value: 2.8, label: "vs last month", isPositive: true }}
          badge={{ text: "Excellent", variant: "default" }}
        />
        <AnalyticsMetrics
          icon={Activity}
          iconColor="text-green-500"
          title="Optimization Score"
          value="87/100"
          description="Overall route efficiency"
          trend={{ value: 5.1, label: "improvement", isPositive: true }}
        />
        <AnalyticsMetrics
          icon={Zap}
          iconColor="text-purple-500"
          title="Cost Savings"
          value="$47K"
          description="This quarter savings"
          trend={{ value: 12.3, label: "vs target", isPositive: true }}
        />
        <AnalyticsMetrics
          icon={TrendingUp}
          iconColor="text-orange-500"
          title="Growth Rate"
          value="23%"
          description="YoY volume growth"
          badge={{ text: "Above Target", variant: "outline" }}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PerformanceChart
          title="Demand Forecast vs Actual"
          description="ML-powered demand prediction accuracy"
          data={demandForecast}
          type="line"
          dataKey="predicted"
          xAxisKey="week"
          color="#3b82f6"
        />
        
        <PerformanceChart
          title="Lane Efficiency Analysis"
          description="Operational efficiency by transportation lane"
          data={efficiencyData}
          type="bar"
          dataKey="efficiency"
          xAxisKey="lane"
          color="#10b981"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <AnalyticsInsights />
        
        <PerformanceChart
          title="Cost Breakdown Analysis"
          description="Transportation cost distribution"
          data={costAnalysis}
          type="bar"
          dataKey="amount"
          xAxisKey="category"
          color="#f59e0b"
        />
      </div>
    </div>
  );
};

export default AnalyticsTab;
