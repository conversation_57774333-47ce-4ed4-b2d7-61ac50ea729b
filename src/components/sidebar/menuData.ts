import {
  Calendar,
  TrendingUp,
  Package,
  Factory,
  Warehouse,
  Truck,
  Users,
  MapPin,
  Settings,
  BarChart3,
  Target,
  Zap,
  Home,
  Database,
  DollarSign,
  Route,
  AlertTriangle,
  Link,
  Building,
  Cog,
  Brain,
  Bot,
  ShoppingCart,
  Wrench
} from "lucide-react";
import { MenuSection } from "./types";

export const menuSections: MenuSection[] = [
  {
    title: "Overview",
    items: [
      { icon: BarChart3, label: "AI Dashboard", active: false, path: "/dashboard" },
      { icon: Target, label: "COG Analysis", active: true, path: "/" },
    ]
  },
  {
    title: "Commerce Mode",
    items: [
      { icon: ShoppingCart, label: "Commerce Hub", active: false, path: "/commerce" },
    ]
  },
  {
    title: "Data Management",
    items: [
      { icon: Calendar, label: "Period", active: false, path: "/period" },
      { icon: TrendingUp, label: "Demand", active: false, path: "/demand" },
      { icon: Database, label: "Master Data", active: false, path: "/master-data" },
    ]
  },
  {
    title: "Network",
    items: [
      { icon: Package, label: "Suppliers", active: false, path: "/suppliers" },
      { 
        icon: Factory, 
        label: "Plants", 
        active: false, 
        path: "/plants", 
        hasSubItems: true,
        subItems: [
          { icon: Building, label: "All Plants", path: "/plants" },
          { icon: Cog, label: "Configuration", path: "/plants-config" }
        ]
      },
      { 
        icon: Warehouse, 
        label: "Warehouses", 
        active: false, 
        path: "/warehouses", 
        hasSubItems: true,
        subItems: [
          { icon: Building, label: "All Warehouses", path: "/warehouses" },
          { icon: Cog, label: "Configuration", path: "/warehouses-config" }
        ]
      },
      { 
        icon: Truck, 
        label: "Transportation", 
        active: false, 
        path: "/transportation", 
        hasSubItems: true,
        subItems: [
          { icon: Route, label: "Network Map", path: "/transportation-network" },
          { icon: DollarSign, label: "Parcel Rates", path: "/parcel-rates" },
          { icon: Truck, label: "Carriers", path: "/carriers" },
          { icon: MapPin, label: "Routes", path: "/routes" }
        ]
      },
    ]
  },
  {
    title: "Constraints & Lanes",
    items: [
      { icon: Route, label: "Lanes", active: false, path: "/lanes" },
      { icon: AlertTriangle, label: "Constraints", active: false, path: "/constraints" },
      { icon: Link, label: "Predef-Lanes Constraints", active: false, path: "/predef-lanes-constraints" },
    ]
  },
  {
    title: "Optimization",
    items: [
      { icon: Brain, label: "Models", active: false, path: "/models" },
      { icon: Zap, label: "Optimize", active: false, path: "/optimize" },
    ]
  },
  {
    title: "Analytics",
    items: [
      { icon: BarChart3, label: "Reports", active: false, path: "/reports" },
    ]
  },
  {
    title: "Infrastructure",
    items: [
      { icon: Wrench, label: "Build", active: false, path: "/build" },
    ]
  },
  {
    title: "System",
    items: [
      { icon: Settings, label: "Settings", active: false, path: "/settings" },
    ]
  }
];
