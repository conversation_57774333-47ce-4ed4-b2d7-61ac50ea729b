
import { LucideIcon } from "lucide-react";

export interface MenuItem {
  icon: LucideIcon;
  label: string;
  active: boolean;
  path: string;
  hasSubItems?: boolean;
  subItems?: SubMenuItem[];
}

export interface SubMenuItem {
  icon: LucideIcon;
  label: string;
  path: string;
}

export interface MenuSection {
  title: string;
  items: MenuItem[];
}

export interface SidebarProps {
  isCollapsed: boolean;
}
