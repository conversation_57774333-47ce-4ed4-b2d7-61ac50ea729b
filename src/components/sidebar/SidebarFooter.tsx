
import { Button } from "@/components/ui/button";
import { Users } from "lucide-react";
import { cn } from "@/lib/utils";

interface SidebarFooterProps {
  isCollapsed: boolean;
}

const SidebarFooter = ({ isCollapsed }: SidebarFooterProps) => {
  return (
    <div className="p-4 border-t border-gray-800">
      <Button
        variant="ghost"
        className={cn(
          "w-full text-gray-300 hover:text-white hover:bg-gray-800",
          isCollapsed ? "justify-center px-2" : "justify-start px-3"
        )}
      >
        <Users className={cn("h-4 w-4", !isCollapsed && "mr-3")} />
        {!isCollapsed && <span className="text-sm">Account</span>}
      </Button>
    </div>
  );
};

export default SidebarFooter;
