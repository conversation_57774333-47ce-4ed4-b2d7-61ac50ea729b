
interface SidebarHeaderProps {
  isCollapsed: boolean;
}

const SidebarHeader = ({ isCollapsed }: SidebarHeaderProps) => {
  return (
    <div className="p-4 border-b border-gray-800">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
          <span className="text-white font-bold text-sm">O</span>
        </div>
        {!isCollapsed && (
          <span className="font-semibold text-lg">Optiflow</span>
        )}
      </div>
    </div>
  );
};

export default SidebarHeader;
