
import { useState } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useNavigate, useLocation } from "react-router-dom";
import { MenuSection } from "./types";

interface SidebarSectionProps {
  section: MenuSection;
  isCollapsed: boolean;
  isExpanded: boolean;
  onToggleSection: (title: string) => void;
}

const SidebarSection = ({ 
  section, 
  isCollapsed, 
  isExpanded, 
  onToggleSection 
}: SidebarSectionProps) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const navigate = useNavigate();
  const location = useLocation();

  const toggleItem = (label: string) => {
    if (isCollapsed) return;
    setExpandedItems(prev => 
      prev.includes(label) 
        ? prev.filter(item => item !== label)
        : [...prev, label]
    );
  };

  const handleNavigation = (path: string, hasSubItems?: boolean) => {
    if (hasSubItems) return;
    navigate(path);
  };

  return (
    <div>
      {/* Section Header */}
      {!isCollapsed && (
        <Button
          variant="ghost"
          className="w-full justify-between text-xs text-gray-400 hover:text-white hover:bg-gray-800 px-3 py-2 h-auto font-medium"
          onClick={() => onToggleSection(section.title)}
        >
          <span>{section.title}</span>
          {isExpanded ? (
            <ChevronDown className="h-3 w-3" />
          ) : (
            <ChevronRight className="h-3 w-3" />
          )}
        </Button>
      )}
      
      {/* Section Items */}
      {(isCollapsed || isExpanded) && (
        <div className={cn("space-y-1", !isCollapsed && "mt-2")}>
          {section.items.map((item) => {
            const Icon = item.icon;
            const isItemExpanded = expandedItems.includes(item.label);
            const isActive = location.pathname === item.path;
            
            return (
              <div key={item.label}>
                <Button
                  variant="ghost"
                  className={cn(
                    "w-full justify-start text-gray-300 hover:text-white hover:bg-gray-800 transition-colors",
                    isActive && "bg-blue-600 text-white hover:bg-blue-700",
                    isCollapsed ? "justify-center px-2 py-3" : "px-3 py-2"
                  )}
                  onClick={() => {
                    if (item.hasSubItems) {
                      toggleItem(item.label);
                    } else {
                      handleNavigation(item.path);
                    }
                  }}
                  title={isCollapsed ? item.label : undefined}
                >
                  <Icon className={cn("h-4 w-4", !isCollapsed && "mr-3")} />
                  {!isCollapsed && (
                    <>
                      <span className="flex-1 text-left text-sm">{item.label}</span>
                      {item.hasSubItems && (
                        isItemExpanded ? (
                          <ChevronDown className="h-3 w-3" />
                        ) : (
                          <ChevronRight className="h-3 w-3" />
                        )
                      )}
                    </>
                  )}
                </Button>
                
                {/* Sub Items */}
                {item.hasSubItems && isItemExpanded && !isCollapsed && item.subItems && (
                  <div className="ml-6 mt-1 space-y-1 border-l border-gray-700 pl-3">
                    {item.subItems.map((subItem) => {
                      const SubIcon = subItem.icon;
                      const isSubActive = location.pathname === subItem.path;
                      
                      return (
                        <Button
                          key={subItem.label}
                          variant="ghost"
                          className={cn(
                            "w-full justify-start text-sm text-gray-400 hover:text-white hover:bg-gray-800 py-1 h-auto",
                            isSubActive && "bg-blue-600 text-white hover:bg-blue-700"
                          )}
                          onClick={() => handleNavigation(subItem.path)}
                        >
                          <SubIcon className="h-3 w-3 mr-2" />
                          {subItem.label}
                        </Button>
                      );
                    })}
                  </div>
                )}
                
                {/* Legacy Sub Items for backward compatibility */}
                {item.hasSubItems && isItemExpanded && !isCollapsed && !item.subItems && (
                  <div className="ml-6 mt-1 space-y-1 border-l border-gray-700 pl-3">
                    <Button
                      variant="ghost"
                      className="w-full justify-start text-sm text-gray-400 hover:text-white hover:bg-gray-800 py-1 h-auto"
                    >
                      View All
                    </Button>
                    <Button
                      variant="ghost"
                      className="w-full justify-start text-sm text-gray-400 hover:text-white hover:bg-gray-800 py-1 h-auto"
                    >
                      Configuration
                    </Button>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default SidebarSection;
