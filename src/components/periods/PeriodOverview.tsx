
import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar } from "lucide-react";
import EditPeriodDialog from "./EditPeriodDialog";

interface Period {
  id: number;
  name: string;
  start: string;
  end: string;
  status: string;
  type: string;
}

interface PeriodOverviewProps {
  periods: Period[];
  onPeriodUpdate: (period: Period) => void;
}

const PeriodOverview = ({ periods, onPeriodUpdate }: PeriodOverviewProps) => {
  const [editingPeriod, setEditingPeriod] = useState<Period | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "bg-green-100 text-green-800";
      case "Planning": return "bg-blue-100 text-blue-800";
      case "Closed": return "bg-gray-100 text-gray-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const handleEditClick = (period: Period) => {
    setEditingPeriod(period);
    setEditDialogOpen(true);
  };

  const handlePeriodSave = (updatedPeriod: Period) => {
    onPeriodUpdate(updatedPeriod);
    setEditingPeriod(null);
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Period List</CardTitle>
          <CardDescription>
            Manage your planning and reporting periods
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {periods.map((period) => (
              <div key={period.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <Calendar className="h-5 w-5 text-gray-500" />
                  <div>
                    <h3 className="font-medium">{period.name}</h3>
                    <p className="text-sm text-gray-600">
                      {period.start} to {period.end} • {period.type}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(period.status)}>
                    {period.status}
                  </Badge>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleEditClick(period)}
                  >
                    Edit
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <EditPeriodDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        period={editingPeriod}
        onSave={handlePeriodSave}
      />
    </>
  );
};

export default PeriodOverview;
