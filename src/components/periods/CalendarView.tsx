
import { Card, CardContent, Card<PERSON>escription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import { useState } from "react";

interface Period {
  id: number;
  name: string;
  start: string;
  end: string;
  status: string;
  type: string;
}

interface CalendarViewProps {
  periods: Period[];
}

const CalendarView = ({ periods }: CalendarViewProps) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "bg-green-500";
      case "Planning": return "bg-blue-500";
      case "Closed": return "bg-gray-500";
      default: return "bg-gray-500";
    }
  };

  const isDateInPeriod = (date: Date, period: Period) => {
    const startDate = new Date(period.start);
    const endDate = new Date(period.end);
    return date >= startDate && date <= endDate;
  };

  const getPeriodsForDate = (date: Date) => {
    return periods.filter(period => isDateInPeriod(date, period));
  };

  const modifiers = {
    activePeriod: (date: Date) => periods.some(p => p.status === "Active" && isDateInPeriod(date, p)),
    planningPeriod: (date: Date) => periods.some(p => p.status === "Planning" && isDateInPeriod(date, p)),
    closedPeriod: (date: Date) => periods.some(p => p.status === "Closed" && isDateInPeriod(date, p)),
  };

  const modifiersStyles = {
    activePeriod: { backgroundColor: '#10b981', color: 'white' },
    planningPeriod: { backgroundColor: '#3b82f6', color: 'white' },
    closedPeriod: { backgroundColor: '#6b7280', color: 'white' },
  };

  const selectedDatePeriods = selectedDate ? getPeriodsForDate(selectedDate) : [];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Period Calendar</CardTitle>
          <CardDescription>
            Visual representation of your periods over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={setSelectedDate}
            modifiers={modifiers}
            modifiersStyles={modifiersStyles}
            className="rounded-md border"
          />
          
          <div className="mt-4 space-y-2">
            <h4 className="text-sm font-medium">Legend:</h4>
            <div className="flex flex-wrap gap-2">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded"></div>
                <span className="text-xs">Active</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded"></div>
                <span className="text-xs">Planning</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-gray-500 rounded"></div>
                <span className="text-xs">Closed</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Selected Date Details</CardTitle>
          <CardDescription>
            {selectedDate 
              ? `Periods for ${selectedDate.toLocaleDateString()}`
              : "Select a date to view period details"
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {selectedDatePeriods.length > 0 ? (
            <div className="space-y-3">
              {selectedDatePeriods.map((period) => (
                <div key={period.id} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{period.name}</h4>
                    <Badge className={`${getStatusColor(period.status)} text-white`}>
                      {period.status}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {period.start} to {period.end} • {period.type}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">
              No periods found for the selected date
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CalendarView;
