
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Settings, Save } from "lucide-react";

const PeriodSettings = () => {
  const [settings, setSettings] = useState({
    defaultPeriodType: "Quarter",
    fiscalYearStart: "January",
    autoCreatePeriods: false,
    allowOverlappingPeriods: false,
    defaultPeriodLength: "3",
    periodNamingPattern: "auto",
    enablePeriodLocking: true,
    requireApprovalForChanges: false,
  });

  const handleSave = () => {
    console.log("Saving period settings:", settings);
    // In a real app, this would save to a backend
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            General Settings
          </CardTitle>
          <CardDescription>
            Configure default settings for period creation and management
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="defaultType">Default Period Type</Label>
              <Select 
                value={settings.defaultPeriodType} 
                onValueChange={(value) => setSettings(prev => ({ ...prev, defaultPeriodType: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select default type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Quarter">Quarter</SelectItem>
                  <SelectItem value="Month">Month</SelectItem>
                  <SelectItem value="Year">Year</SelectItem>
                  <SelectItem value="Week">Week</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="fiscalStart">Fiscal Year Start</Label>
              <Select 
                value={settings.fiscalYearStart} 
                onValueChange={(value) => setSettings(prev => ({ ...prev, fiscalYearStart: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select fiscal year start" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="January">January</SelectItem>
                  <SelectItem value="February">February</SelectItem>
                  <SelectItem value="March">March</SelectItem>
                  <SelectItem value="April">April</SelectItem>
                  <SelectItem value="May">May</SelectItem>
                  <SelectItem value="June">June</SelectItem>
                  <SelectItem value="July">July</SelectItem>
                  <SelectItem value="August">August</SelectItem>
                  <SelectItem value="September">September</SelectItem>
                  <SelectItem value="October">October</SelectItem>
                  <SelectItem value="November">November</SelectItem>
                  <SelectItem value="December">December</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="defaultLength">Default Period Length (months)</Label>
            <Input
              id="defaultLength"
              type="number"
              min="1"
              max="12"
              value={settings.defaultPeriodLength}
              onChange={(e) => setSettings(prev => ({ ...prev, defaultPeriodLength: e.target.value }))}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="namingPattern">Period Naming Pattern</Label>
            <Select 
              value={settings.periodNamingPattern} 
              onValueChange={(value) => setSettings(prev => ({ ...prev, periodNamingPattern: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select naming pattern" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="auto">Auto-generate (Q1 2024, Q2 2024, etc.)</SelectItem>
                <SelectItem value="manual">Manual entry required</SelectItem>
                <SelectItem value="template">Use custom template</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Automation Settings</CardTitle>
          <CardDescription>
            Configure automatic period creation and validation rules
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="autoCreate">Auto-create periods</Label>
              <p className="text-sm text-gray-600">
                Automatically create new periods based on fiscal calendar
              </p>
            </div>
            <Switch
              id="autoCreate"
              checked={settings.autoCreatePeriods}
              onCheckedChange={(checked) => setSettings(prev => ({ ...prev, autoCreatePeriods: checked }))}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="allowOverlapping">Allow overlapping periods</Label>
              <p className="text-sm text-gray-600">
                Permit periods with overlapping date ranges
              </p>
            </div>
            <Switch
              id="allowOverlapping"
              checked={settings.allowOverlappingPeriods}
              onCheckedChange={(checked) => setSettings(prev => ({ ...prev, allowOverlappingPeriods: checked }))}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Security & Approval Settings</CardTitle>
          <CardDescription>
            Configure period locking and approval workflows
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="enableLocking">Enable period locking</Label>
              <p className="text-sm text-gray-600">
                Allow periods to be locked to prevent further changes
              </p>
            </div>
            <Switch
              id="enableLocking"
              checked={settings.enablePeriodLocking}
              onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enablePeriodLocking: checked }))}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="requireApproval">Require approval for changes</Label>
              <p className="text-sm text-gray-600">
                Period modifications require manager approval
              </p>
            </div>
            <Switch
              id="requireApproval"
              checked={settings.requireApprovalForChanges}
              onCheckedChange={(checked) => setSettings(prev => ({ ...prev, requireApprovalForChanges: checked }))}
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSave} className="flex items-center gap-2">
          <Save className="h-4 w-4" />
          Save Settings
        </Button>
      </div>
    </div>
  );
};

export default PeriodSettings;
