
import { Card, CardContent } from "@/components/ui/card";
import { Calendar, Clock, Settings } from "lucide-react";

interface Period {
  id: number;
  name: string;
  start: string;
  end: string;
  status: string;
  type: string;
}

interface PeriodSummaryCardsProps {
  periods: Period[];
}

const PeriodSummaryCards = ({ periods }: PeriodSummaryCardsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-blue-500" />
            <div>
              <p className="text-sm text-gray-600">Active Periods</p>
              <p className="text-2xl font-bold">{periods.filter(p => p.status === "Active").length}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-green-500" />
            <div>
              <p className="text-sm text-gray-600">Planning Periods</p>
              <p className="text-2xl font-bold">{periods.filter(p => p.status === "Planning").length}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-purple-500" />
            <div>
              <p className="text-sm text-gray-600">Closed Periods</p>
              <p className="text-2xl font-bold">{periods.filter(p => p.status === "Closed").length}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-orange-500" />
            <div>
              <p className="text-sm text-gray-600">Total Periods</p>
              <p className="text-2xl font-bold">{periods.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PeriodSummaryCards;
