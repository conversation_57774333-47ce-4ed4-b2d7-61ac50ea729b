
import { useState } from "react";
import { Maximize2, Minimize2, Layers, Filter } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

const MapContainer = () => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showLegend, setShowLegend] = useState(true);

  return (
    <Card className="relative h-96 bg-gradient-to-br from-blue-50 to-green-50 border-0 shadow-lg overflow-hidden">
      {/* Map placeholder with sample data visualization */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="relative w-full h-full bg-gradient-to-br from-blue-100 via-green-100 to-blue-200">
          {/* Sample map markers */}
          <div className="absolute top-1/4 left-1/3 w-6 h-6 bg-yellow-500 rounded-full shadow-lg animate-pulse"></div>
          <div className="absolute top-1/2 left-1/2 w-8 h-8 bg-blue-600 rounded-full shadow-lg flex items-center justify-center">
            <div className="w-3 h-3 bg-white rounded-full"></div>
          </div>
          <div className="absolute bottom-1/3 right-1/4 w-6 h-6 bg-red-500 rounded-full shadow-lg"></div>
          
          {/* Connection lines */}
          <svg className="absolute inset-0 w-full h-full">
            <line x1="33%" y1="25%" x2="50%" y2="50%" stroke="#ef4444" strokeWidth="2" opacity="0.7"/>
            <line x1="50%" y1="50%" x2="75%" y2="67%" stroke="#ef4444" strokeWidth="2" opacity="0.7"/>
          </svg>
        </div>
      </div>

      {/* Map controls */}
      <div className="absolute top-4 right-4 flex flex-col gap-2">
        <Button
          variant="secondary"
          size="sm"
          onClick={() => setIsFullscreen(!isFullscreen)}
          className="bg-white/90 hover:bg-white shadow-md"
        >
          {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
        </Button>
        
        <Button
          variant="secondary"
          size="sm"
          onClick={() => setShowLegend(!showLegend)}
          className="bg-white/90 hover:bg-white shadow-md"
        >
          <Layers className="h-4 w-4" />
        </Button>
        
        <Button
          variant="secondary"
          size="sm"
          className="bg-white/90 hover:bg-white shadow-md"
        >
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      {/* Legend */}
      {showLegend && (
        <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg p-3 shadow-lg">
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
              <span>Customers</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-blue-600 rounded-full"></div>
              <span>COG</span>
            </div>
          </div>
        </div>
      )}

      {/* Map/Satellite toggle */}
      <div className="absolute top-4 left-4">
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <Button variant="ghost" size="sm" className="bg-blue-600 text-white hover:bg-blue-700">
            Map
          </Button>
          <Button variant="ghost" size="sm" className="hover:bg-gray-100">
            Satellite
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default MapContainer;
