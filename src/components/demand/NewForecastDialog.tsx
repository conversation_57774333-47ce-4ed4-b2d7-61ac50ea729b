
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Plus } from "lucide-react";

interface NewForecastDialogProps {
  onForecastCreate: (forecast: any) => void;
}

const NewForecastDialog = ({ onForecastCreate }: NewForecastDialogProps) => {
  const [open, setOpen] = useState(false);
  const [product, setProduct] = useState("");
  const [region, setRegion] = useState("");
  const [forecast, setForecast] = useState("");
  const [notes, setNotes] = useState("");

  const handleSubmit = () => {
    if (product && region && forecast) {
      const newForecast = {
        id: Date.now(),
        product,
        region,
        forecast: parseInt(forecast),
        actual: 0,
        variance: 0,
        status: "Planning",
        notes
      };
      onForecastCreate(newForecast);
      setOpen(false);
      setProduct("");
      setRegion("");
      setForecast("");
      setNotes("");
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="sm">
          <Plus className="h-4 w-4 mr-2" />
          New Forecast
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Forecast</DialogTitle>
          <DialogDescription>
            Add a new demand forecast for a product and region.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="product">Product</Label>
            <Select value={product} onValueChange={setProduct}>
              <SelectTrigger>
                <SelectValue placeholder="Select product" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Product A">Product A</SelectItem>
                <SelectItem value="Product B">Product B</SelectItem>
                <SelectItem value="Product C">Product C</SelectItem>
                <SelectItem value="Product D">Product D</SelectItem>
                <SelectItem value="Product E">Product E</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="region">Region</Label>
            <Select value={region} onValueChange={setRegion}>
              <SelectTrigger>
                <SelectValue placeholder="Select region" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="North America">North America</SelectItem>
                <SelectItem value="Europe">Europe</SelectItem>
                <SelectItem value="Asia Pacific">Asia Pacific</SelectItem>
                <SelectItem value="Latin America">Latin America</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="forecast">Forecast Quantity</Label>
            <Input
              id="forecast"
              type="number"
              value={forecast}
              onChange={(e) => setForecast(e.target.value)}
              placeholder="Enter forecast quantity"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Additional notes about this forecast"
              rows={3}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>Create Forecast</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default NewForecastDialog;
