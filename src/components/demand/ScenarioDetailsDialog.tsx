
import { Dialog, Di<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CalendarDays, TrendingUp, Target, Clock } from 'lucide-react';

interface Scenario {
  id: number;
  name: string;
  description: string;
  status: string;
  accuracy: number;
}

interface ScenarioDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  scenario: Scenario | null;
}

const ScenarioDetailsDialog = ({ open, onOpenChange, scenario }: ScenarioDetailsDialogProps) => {
  if (!scenario) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            {scenario.name}
            <Badge variant={scenario.status === "Active" ? "default" : "secondary"}>
              {scenario.status}
            </Badge>
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <div>
            <h4 className="font-medium mb-2">Description</h4>
            <p className="text-gray-600">{scenario.description}</p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-600">Forecast Accuracy</p>
                    <p className="text-2xl font-bold">{scenario.accuracy}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <CalendarDays className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-600">Time Horizon</p>
                    <p className="text-2xl font-bold">12</p>
                    <p className="text-sm text-gray-600">months</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Scenario Metrics</CardTitle>
              <CardDescription>Key performance indicators for this scenario</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">Total Forecast</span>
                  </div>
                  <p className="text-lg font-semibold">42.08K units</p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-purple-500" />
                    <span className="text-sm font-medium">Last Updated</span>
                  </div>
                  <p className="text-lg font-semibold">2 days ago</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Assumptions</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Market growth rate: 5% annually</li>
                <li>• Seasonal variations accounted for</li>
                <li>• Economic conditions remain stable</li>
                <li>• No major supply chain disruptions</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ScenarioDetailsDialog;
