
import { useState } from "react";
import { ChevronDown, ChevronUp, Eye, EyeOff, Plus, MoreHorizontal, Filter, Search, Archive, Trash2, Copy, Edit, Play, RefreshCw } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ModelData } from "@/types/model";
import { ModelLifecycleService } from "@/services/modelLifecycleService";
import ModelDialog from "./ModelDialog";
import ConfirmationDialog from "./ConfirmationDialog";
import { useToast } from "@/hooks/use-toast";

// Sample model data - in a real app, this would come from an API
const sampleModels: ModelData[] = [
  {
    id: "model_1",
    name: "Network Flow Optimization",
    type: "network-flow",
    status: "active",
    version: "2.1.0",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-20T14:45:00Z",
    createdBy: "John Doe",
    description: "Optimizes network flow and transportation costs using Gurobi solver",
    tags: ["gurobi", "network-flow", "cost-optimization"],
    lastRunAt: "2024-01-20T14:45:00Z",
    accuracy: 98.5,
    solver: "gurobi",
    objectiveFunction: "minimize-cost",
  },
  {
    id: "model_2",
    name: "Inventory Optimization",
    type: "inventory",
    status: "draft",
    version: "1.0.0",
    createdAt: "2024-01-18T09:15:00Z",
    updatedAt: "2024-01-18T09:15:00Z",
    createdBy: "Jane Smith",
    description: "Multi-echelon inventory optimization model",
    tags: ["inventory", "multi-echelon"],
    accuracy: 94.2,
    solver: "gurobi",
    objectiveFunction: "balance",
  },
  {
    id: "model_3",
    name: "Vehicle Routing Template",
    type: "routing",
    status: "archived",
    version: "3.0.0",
    createdAt: "2023-12-10T16:20:00Z",
    updatedAt: "2024-01-05T11:30:00Z",
    createdBy: "Admin",
    description: "Template for vehicle routing problem optimization",
    tags: ["template", "vrp", "routing"],
    isTemplate: true,
    lastRunAt: "2024-01-05T11:30:00Z",
    accuracy: 96.8,
    solver: "or-tools",
    objectiveFunction: "minimize-cost",
  },
];

const DataTable = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [sortField, setSortField] = useState<keyof ModelData | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [models, setModels] = useState<ModelData[]>(sampleModels);
  
  // Dialog states
  const [modelDialog, setModelDialog] = useState<{ open: boolean; mode: 'create' | 'edit' | 'duplicate'; model?: ModelData }>({
    open: false,
    mode: 'create',
  });
  const [confirmDialog, setConfirmDialog] = useState<{ open: boolean; title: string; description: string; action?: () => void }>({
    open: false,
    title: '',
    description: '',
  });

  const { toast } = useToast();

  const handleSort = (field: keyof ModelData) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const handleSelectModel = (modelId: string) => {
    setSelectedModels(prev => 
      prev.includes(modelId) 
        ? prev.filter(id => id !== modelId)
        : [...prev, modelId]
    );
  };

  const handleSelectAll = () => {
    if (selectedModels.length === filteredData.length) {
      setSelectedModels([]);
    } else {
      setSelectedModels(filteredData.map(model => model.id));
    }
  };

  const filteredData = models.filter(model => {
    const matchesSearch = Object.values(model).some(value =>
      value?.toString().toLowerCase().includes(searchTerm.toLowerCase())
    );
    const matchesStatus = statusFilter === "all" || model.status === statusFilter;
    const matchesType = typeFilter === "all" || model.type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortField) return 0;
    
    const aVal = a[sortField];
    const bVal = b[sortField];
    
    if (typeof aVal === "number" && typeof bVal === "number") {
      return sortDirection === "asc" ? aVal - bVal : bVal - aVal;
    }
    
    return sortDirection === "asc" 
      ? aVal?.toString().localeCompare(bVal?.toString() || '')
      : bVal?.toString().localeCompare(aVal?.toString() || '');
  });

  const handleModelAction = async (action: string, model: ModelData) => {
    try {
      switch (action) {
        case 'edit':
          setModelDialog({ open: true, mode: 'edit', model });
          break;
        case 'duplicate':
          setModelDialog({ open: true, mode: 'duplicate', model });
          break;
        case 'archive':
          setConfirmDialog({
            open: true,
            title: 'Archive Model',
            description: `Are you sure you want to archive "${model.name}"? This will make it inactive but preserve all data.`,
            action: () => handleArchiveModel(model.id),
          });
          break;
        case 'delete':
          setConfirmDialog({
            open: true,
            title: 'Delete Model',
            description: `Are you sure you want to permanently delete "${model.name}"? This action cannot be undone.`,
            action: () => handleDeleteModel(model.id),
          });
          break;
        case 'promote':
          await ModelLifecycleService.promoteModel(model.id);
          setModels(prev => prev.map(m => m.id === model.id ? { ...m, status: 'active' as const } : m));
          toast({ title: "Model promoted to active" });
          break;
        case 'restore':
          await ModelLifecycleService.updateModelStatus(model.id, 'draft');
          setModels(prev => prev.map(m => m.id === model.id ? { ...m, status: 'draft' as const } : m));
          toast({ title: "Model restored" });
          break;
      }
    } catch (error) {
      toast({ title: "Action failed", description: "Please try again", variant: "destructive" });
    }
  };

  const handleArchiveModel = async (modelId: string) => {
    await ModelLifecycleService.archiveModel(modelId);
    setModels(prev => prev.map(m => m.id === modelId ? { ...m, status: 'archived' as const } : m));
    toast({ title: "Model archived successfully" });
  };

  const handleDeleteModel = async (modelId: string) => {
    await ModelLifecycleService.deleteModel(modelId);
    setModels(prev => prev.filter(m => m.id !== modelId));
    setSelectedModels(prev => prev.filter(id => id !== modelId));
    toast({ title: "Model deleted successfully" });
  };

  const handleSaveModel = (savedModel: ModelData) => {
    if (modelDialog.mode === 'create' || modelDialog.mode === 'duplicate') {
      setModels(prev => [...prev, savedModel]);
    } else {
      setModels(prev => prev.map(m => m.id === savedModel.id ? savedModel : m));
    }
    toast({ title: `Model ${modelDialog.mode === 'create' ? 'created' : modelDialog.mode === 'duplicate' ? 'duplicated' : 'updated'} successfully` });
  };

  const handleBulkArchive = () => {
    setConfirmDialog({
      open: true,
      title: 'Archive Selected Models',
      description: `Are you sure you want to archive ${selectedModels.length} selected models?`,
      action: async () => {
        for (const modelId of selectedModels) {
          await ModelLifecycleService.archiveModel(modelId);
        }
        setModels(prev => prev.map(m => selectedModels.includes(m.id) ? { ...m, status: 'archived' as const } : m));
        setSelectedModels([]);
        toast({ title: `${selectedModels.length} models archived` });
      },
    });
  };

  const getStatusColor = (status: ModelData['status']) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'archived': return 'bg-gray-100 text-gray-800';
      case 'deprecated': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: ModelData['type']) => {
    switch (type) {
      case 'network-flow': return 'bg-blue-100 text-blue-800';
      case 'inventory': return 'bg-green-100 text-green-800';
      case 'routing': return 'bg-purple-100 text-purple-800';
      case 'facility-location': return 'bg-orange-100 text-orange-800';
      case 'scheduling': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const SortIcon = ({ field }: { field: keyof ModelData }) => {
    if (sortField !== field) return null;
    return sortDirection === "asc" ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />;
  };

  return (
    <>
      <Card className="shadow-lg border-0">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">Model Management</CardTitle>
            <div className="flex items-center gap-3">
              <Button
                onClick={() => setModelDialog({ open: true, mode: 'create' })}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Create Model
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="flex items-center gap-2"
              >
                {isCollapsed ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                {isCollapsed ? "Show" : "Hide"}
              </Button>
            </div>
          </div>
        </CardHeader>
        
        {!isCollapsed && (
          <CardContent>
            {/* Filters and Search */}
            <div className="flex items-center gap-4 mb-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search models..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                  <SelectItem value="deprecated">Deprecated</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="network-flow">Network Flow</SelectItem>
                  <SelectItem value="inventory">Inventory</SelectItem>
                  <SelectItem value="routing">Vehicle Routing</SelectItem>
                  <SelectItem value="facility-location">Facility Location</SelectItem>
                  <SelectItem value="scheduling">Scheduling</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Bulk Actions */}
            {selectedModels.length > 0 && (
              <div className="flex items-center gap-2 mb-4 p-3 bg-blue-50 rounded-lg">
                <span className="text-sm font-medium">{selectedModels.length} models selected</span>
                <Button size="sm" variant="outline" onClick={handleBulkArchive}>
                  <Archive className="h-4 w-4 mr-1" />
                  Archive Selected
                </Button>
                <Button size="sm" variant="outline">
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete Selected
                </Button>
              </div>
            )}

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 w-12">
                      <Checkbox
                        checked={selectedModels.length === filteredData.length && filteredData.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </th>
                    {[
                      { key: 'name' as keyof ModelData, label: 'Model Name', width: 'w-48' },
                      { key: 'type' as keyof ModelData, label: 'Type', width: 'w-32' },
                      { key: 'status' as keyof ModelData, label: 'Status', width: 'w-24' },
                      { key: 'version' as keyof ModelData, label: 'Version', width: 'w-20' },
                      { key: 'accuracy' as keyof ModelData, label: 'Accuracy', width: 'w-24' },
                      { key: 'updatedAt' as keyof ModelData, label: 'Last Updated', width: 'w-32' },
                      { key: 'createdBy' as keyof ModelData, label: 'Created By', width: 'w-32' },
                    ].map(({ key, label, width }) => (
                      <th
                        key={key}
                        className={`text-left py-3 px-4 font-medium text-gray-700 cursor-pointer hover:bg-gray-50 transition-colors ${width}`}
                        onClick={() => handleSort(key)}
                      >
                        <div className="flex items-center gap-2">
                          {label}
                          <SortIcon field={key} />
                        </div>
                      </th>
                    ))}
                    <th className="text-left py-3 px-4 w-20">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {sortedData.map((model) => (
                    <tr key={model.id} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                      <td className="py-3 px-4">
                        <Checkbox
                          checked={selectedModels.includes(model.id)}
                          onCheckedChange={() => handleSelectModel(model.id)}
                        />
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex flex-col">
                          <span className="font-medium text-sm">{model.name}</span>
                          {model.isTemplate && (
                            <Badge variant="outline" className="w-fit text-xs mt-1">Template</Badge>
                          )}
                          {model.tags && model.tags.length > 0 && (
                            <div className="flex gap-1 mt-1">
                              {model.tags.slice(0, 2).map(tag => (
                                <Badge key={tag} variant="secondary" className="text-xs">{tag}</Badge>
                              ))}
                              {model.tags.length > 2 && (
                                <Badge variant="secondary" className="text-xs">+{model.tags.length - 2}</Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Badge className={`text-xs ${getTypeColor(model.type)}`}>
                          {model.type}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <Badge className={`text-xs ${getStatusColor(model.status)}`}>
                          {model.status}
                        </Badge>
                      </td>
                      <td className="py-3 px-4 text-sm font-mono">{model.version}</td>
                      <td className="py-3 px-4 text-sm">
                        {model.accuracy ? `${model.accuracy}%` : '-'}
                      </td>
                      <td className="py-3 px-4 text-sm">
                        {new Date(model.updatedAt).toLocaleDateString()}
                      </td>
                      <td className="py-3 px-4 text-sm">{model.createdBy}</td>
                      <td className="py-3 px-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {ModelLifecycleService.getLifecycleActions(model).map((action) => (
                              <DropdownMenuItem
                                key={action.type}
                                onClick={() => handleModelAction(action.type, model)}
                                className={action.variant === 'destructive' ? 'text-red-600' : ''}
                              >
                                {action.type === 'edit' && <Edit className="h-4 w-4 mr-2" />}
                                {action.type === 'duplicate' && <Copy className="h-4 w-4 mr-2" />}
                                {action.type === 'archive' && <Archive className="h-4 w-4 mr-2" />}
                                {action.type === 'delete' && <Trash2 className="h-4 w-4 mr-2" />}
                                {action.type === 'promote' && <Play className="h-4 w-4 mr-2" />}
                                {action.type === 'restore' && <RefreshCw className="h-4 w-4 mr-2" />}
                                {action.label}
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Model Dialog */}
      <ModelDialog
        open={modelDialog.open}
        onOpenChange={(open) => setModelDialog(prev => ({ ...prev, open }))}
        mode={modelDialog.mode}
        model={modelDialog.model}
        onSave={handleSaveModel}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog(prev => ({ ...prev, open }))}
        title={confirmDialog.title}
        description={confirmDialog.description}
        variant="destructive"
        onConfirm={() => confirmDialog.action?.()}
      />
    </>
  );
};

export default DataTable;
