
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  TrendingUp, 
  TrendingDown, 
  Brain, 
  Zap,
  Target,
  AlertTriangle
} from "lucide-react";

const SmartAnalytics = () => {
  const metrics = [
    {
      title: "AI Efficiency Score",
      value: "94.2%",
      change: "+2.1%",
      trend: "up",
      prediction: "Projected to reach 96% by month end",
      icon: Brain,
      color: "text-purple-600"
    },
    {
      title: "Predictive Cost Savings",
      value: "$127.5K",
      change: "+$23.2K",
      trend: "up",
      prediction: "AI recommends 3 optimization actions",
      icon: Target,
      color: "text-green-600"
    },
    {
      title: "Risk Assessment",
      value: "Low",
      change: "Improved",
      trend: "up",
      prediction: "2 potential risks identified and mitigated",
      icon: AlertTriangle,
      color: "text-yellow-600"
    },
    {
      title: "Auto-Optimization",
      value: "12",
      change: "+4",
      trend: "up",
      prediction: "Routes optimized automatically this week",
      icon: Zap,
      color: "text-blue-600"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {metrics.map((metric, index) => {
        const Icon = metric.icon;
        return (
          <Card key={index} className="relative overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {metric.title}
              </CardTitle>
              <Icon className={`h-4 w-4 ${metric.color}`} />
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2 mb-2">
                <div className="text-2xl font-bold">{metric.value}</div>
                <Badge variant="outline" className={`${
                  metric.trend === 'up' ? 'text-green-600 border-green-200' : 'text-red-600 border-red-200'
                }`}>
                  {metric.trend === 'up' ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
                  {metric.change}
                </Badge>
              </div>
              <p className="text-xs text-gray-500">{metric.prediction}</p>
              <div className="absolute top-0 right-0 w-2 h-full bg-gradient-to-b from-blue-400 to-purple-600 opacity-20"></div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default SmartAnalytics;
