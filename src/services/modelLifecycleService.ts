
import { ModelData, ModelLifecycleAction } from '@/types/model';

export class ModelLifecycleService {
  static async createModel(data: Partial<ModelData>): Promise<ModelData> {
    const newModel: ModelData = {
      id: `model_${Date.now()}`,
      name: data.name || 'New Model',
      type: data.type || 'network-flow',
      status: 'draft',
      version: '1.0.0',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'Current User',
      description: data.description,
      tags: data.tags || [],
      isTemplate: data.isTemplate || false,
      parentModelId: data.parentModelId,
    };
    
    console.log('Creating new model:', newModel);
    return newModel;
  }

  static async duplicateModel(originalModel: ModelData): Promise<ModelData> {
    const duplicatedModel = await this.createModel({
      ...originalModel,
      name: `${originalModel.name} (Copy)`,
      parentModelId: originalModel.id,
      version: '1.0.0',
    });
    
    console.log('Duplicated model:', duplicatedModel);
    return duplicatedModel;
  }

  static async updateModelStatus(modelId: string, status: ModelData['status']): Promise<void> {
    console.log(`Updating model ${modelId} status to ${status}`);
    // In a real app, this would make an API call
  }

  static async archiveModel(modelId: string): Promise<void> {
    await this.updateModelStatus(modelId, 'archived');
    console.log(`Archived model ${modelId}`);
  }

  static async deleteModel(modelId: string): Promise<void> {
    console.log(`Deleting model ${modelId}`);
    // In a real app, this would make an API call
  }

  static async promoteModel(modelId: string): Promise<void> {
    await this.updateModelStatus(modelId, 'active');
    console.log(`Promoted model ${modelId} to active`);
  }

  static getLifecycleActions(model: ModelData): ModelLifecycleAction[] {
    const baseActions: ModelLifecycleAction[] = [
      { type: 'edit', label: 'Edit', icon: 'Edit', variant: 'outline' },
      { type: 'duplicate', label: 'Duplicate', icon: 'Copy', variant: 'outline' },
    ];

    if (model.status === 'draft') {
      baseActions.push({ type: 'promote', label: 'Activate', icon: 'Play', variant: 'default' });
    }

    if (model.status === 'active') {
      baseActions.push({ type: 'deprecate', label: 'Deprecate', icon: 'Archive', variant: 'outline' });
    }

    if (model.status !== 'archived') {
      baseActions.push({ 
        type: 'archive', 
        label: 'Archive', 
        icon: 'Archive', 
        variant: 'outline',
        requiresConfirmation: true 
      });
    }

    if (model.status === 'archived') {
      baseActions.push({ type: 'restore', label: 'Restore', icon: 'RefreshCw', variant: 'outline' });
    }

    baseActions.push({ 
      type: 'delete', 
      label: 'Delete', 
      icon: 'Trash2', 
      variant: 'destructive',
      requiresConfirmation: true 
    });

    return baseActions;
  }
}
