import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import Models from "./pages/Models";
import Commerce from "./pages/Commerce";
import Optimize from "./pages/Optimize";
import Reports from "./pages/Reports";
import Settings from "./pages/Settings";
import ParcelRates from "./pages/ParcelRates";
import TransportationNetwork from "./pages/TransportationNetwork";
import Carriers from "./pages/Carriers";
import RoutesPage from "./pages/Routes";
import Period from "./pages/Period";
import Demand from "./pages/Demand";
import MasterData from "./pages/MasterData";
import Suppliers from "./pages/Suppliers";
import Lanes from "./pages/Lanes";
import Constraints from "./pages/Constraints";
import PredefLanesConstraints from "./pages/PredefLanesConstraints";
import Plants from "./pages/Plants";
import Warehouses from "./pages/Warehouses";
import PlantConfig from "./pages/PlantConfig";
import WarehouseConfig from "./pages/WarehouseConfig";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/models" element={<Models />} />
          <Route path="/commerce" element={<Commerce />} />
          <Route path="/optimize" element={<Optimize />} />
          <Route path="/reports" element={<Reports />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/parcel-rates" element={<ParcelRates />} />
          <Route path="/transportation-network" element={<TransportationNetwork />} />
          <Route path="/carriers" element={<Carriers />} />
          <Route path="/routes" element={<RoutesPage />} />
          <Route path="/period" element={<Period />} />
          <Route path="/demand" element={<Demand />} />
          <Route path="/master-data" element={<MasterData />} />
          <Route path="/suppliers" element={<Suppliers />} />
          <Route path="/lanes" element={<Lanes />} />
          <Route path="/constraints" element={<Constraints />} />
          <Route path="/predef-lanes-constraints" element={<PredefLanesConstraints />} />
          <Route path="/plants" element={<Plants />} />
          <Route path="/plants-config" element={<PlantConfig />} />
          <Route path="/warehouses" element={<Warehouses />} />
          <Route path="/warehouses-config" element={<WarehouseConfig />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
