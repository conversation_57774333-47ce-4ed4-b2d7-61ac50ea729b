
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import NewPeriodDialog from "@/components/periods/NewPeriodDialog";
import ImportExportUtils from "@/components/shared/ImportExportUtils";
import PeriodSummaryCards from "@/components/periods/PeriodSummaryCards";
import PeriodOverview from "@/components/periods/PeriodOverview";
import CalendarView from "@/components/periods/CalendarView";
import PeriodSettings from "@/components/periods/PeriodSettings";

const Period = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [periods, setPeriods] = useState([
    { id: 1, name: "Q4 2024", start: "2024-10-01", end: "2024-12-31", status: "Active", type: "Quarter" },
    { id: 2, name: "Q1 2025", start: "2025-01-01", end: "2025-03-31", status: "Planning", type: "Quarter" },
    { id: 3, name: "FY 2024", start: "2024-01-01", end: "2024-12-31", status: "Closed", type: "Year" },
    { id: 4, name: "November 2024", start: "2024-11-01", end: "2024-11-30", status: "Active", type: "Month" },
  ]);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handlePeriodCreate = (newPeriod: any) => {
    setPeriods([...periods, newPeriod]);
  };

  const handlePeriodUpdate = (updatedPeriod: any) => {
    setPeriods(periods.map(period => 
      period.id === updatedPeriod.id ? updatedPeriod : period
    ));
  };

  const handlePeriodsImport = (importedPeriods: any[]) => {
    setPeriods([...periods, ...importedPeriods]);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Period Management</h1>
              <p className="text-gray-600 mt-1">Configure and manage time periods for planning and analysis</p>
            </div>
            
            <div className="flex items-center gap-3">
              <ImportExportUtils 
                data={periods} 
                filename="periods" 
                onImport={handlePeriodsImport}
              />
              <NewPeriodDialog onPeriodCreate={handlePeriodCreate} />
            </div>
          </div>

          {/* Summary Cards */}
          <PeriodSummaryCards periods={periods} />

          {/* Main Content */}
          <Tabs defaultValue="periods" className="space-y-4">
            <TabsList>
              <TabsTrigger value="periods">Period Overview</TabsTrigger>
              <TabsTrigger value="calendar">Calendar View</TabsTrigger>
              <TabsTrigger value="settings">Period Settings</TabsTrigger>
            </TabsList>
            
            <TabsContent value="periods" className="space-y-4">
              <PeriodOverview 
                periods={periods} 
                onPeriodUpdate={handlePeriodUpdate}
              />
            </TabsContent>

            <TabsContent value="calendar" className="space-y-4">
              <CalendarView periods={periods} />
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <PeriodSettings />
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default Period;
