
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { Constraint, Violation, ComplianceMetric } from "@/types/constraint";
import TabNavigation from "@/components/constraints/TabNavigation";
import ConstraintsTab from "@/components/constraints/ConstraintsTab";
import ViolationsTab from "@/components/constraints/ViolationsTab";
import ComplianceTab from "@/components/constraints/ComplianceTab";

const Constraints = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [violationSearchTerm, setViolationSearchTerm] = useState("");
  const [constraintSearchTerm, setConstraintSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("violations");

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const constraints: Constraint[] = [
    {
      id: 1,
      name: "Driver Hours - DOT Regulation",
      type: "Time Constraint",
      category: "Regulatory",
      status: "Active",
      priority: "Critical",
      description: "Maximum 11 hours driving time per day",
      lastModified: "2024-06-05",
      appliedTo: "All Drivers"
    },
    {
      id: 2,
      name: "Delivery Window - Downtown",
      type: "Time Window",
      category: "Operational",
      status: "Active",
      priority: "High",
      description: "Deliveries only between 7AM-11AM in downtown area",
      lastModified: "2024-06-03",
      appliedTo: "Downtown Routes"
    },
    {
      id: 3,
      name: "Hazmat Route Restriction",
      type: "Route Constraint",
      category: "Safety",
      status: "Active",
      priority: "Critical",
      description: "Hazmat vehicles cannot use bridge routes",
      lastModified: "2024-06-01",
      appliedTo: "Hazmat Vehicles"
    },
    {
      id: 4,
      name: "Vehicle Capacity Limit",
      type: "Capacity Constraint",
      category: "Operational",
      status: "Active",
      priority: "Medium",
      description: "Maximum weight capacity per vehicle type",
      lastModified: "2024-05-28",
      appliedTo: "All Vehicles"
    }
  ];

  const violations: Violation[] = [
    {
      id: 1,
      constraintName: "Driver Hours - DOT Regulation",
      violationType: "Time Exceeded",
      severity: "Critical",
      driver: "John Smith",
      vehicle: "TRK-001",
      route: "Chicago to Detroit",
      timestamp: "2024-06-07 14:30",
      duration: "12.5 hours",
      status: "Under Review",
      description: "Driver exceeded 11-hour limit by 1.5 hours"
    },
    {
      id: 2,
      constraintName: "Delivery Window - Downtown",
      violationType: "Time Window",
      severity: "Medium",
      driver: "Sarah Johnson",
      vehicle: "TRK-005",
      route: "Chicago Downtown",
      timestamp: "2024-06-07 12:15",
      duration: "N/A",
      status: "Resolved",
      description: "Delivery attempted outside 7AM-11AM window"
    },
    {
      id: 3,
      constraintName: "Hazmat Route Restriction",
      violationType: "Route Violation",
      severity: "Critical",
      driver: "Mike Wilson",
      vehicle: "HAZ-003",
      route: "Bridge Route 90",
      timestamp: "2024-06-06 09:45",
      duration: "N/A",
      status: "Pending Investigation",
      description: "Hazmat vehicle used restricted bridge route"
    }
  ];

  const complianceMetrics: ComplianceMetric[] = [
    {
      category: "Driver Hours",
      totalChecks: 245,
      violations: 12,
      complianceRate: 95.1,
      trend: "up"
    },
    {
      category: "Route Restrictions",
      totalChecks: 189,
      violations: 3,
      complianceRate: 98.4,
      trend: "stable"
    },
    {
      category: "Time Windows",
      totalChecks: 432,
      violations: 28,
      complianceRate: 93.5,
      trend: "down"
    },
    {
      category: "Vehicle Capacity",
      totalChecks: 156,
      violations: 5,
      complianceRate: 96.8,
      trend: "up"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Constraints & Compliance</h1>
              <p className="text-gray-600 mt-1">Manage constraints and monitor compliance across operations</p>
            </div>
          </div>

          <TabNavigation activeTab={activeTab} setActiveTab={setActiveTab} />

          {activeTab === "constraints" && (
            <ConstraintsTab
              constraints={constraints}
              searchTerm={constraintSearchTerm}
              setSearchTerm={setConstraintSearchTerm}
            />
          )}

          {activeTab === "violations" && (
            <ViolationsTab
              violations={violations}
              searchTerm={violationSearchTerm}
              setSearchTerm={setViolationSearchTerm}
            />
          )}

          {activeTab === "compliance" && (
            <ComplianceTab complianceMetrics={complianceMetrics} />
          )}
        </main>
      </div>
    </div>
  );
};

export default Constraints;
