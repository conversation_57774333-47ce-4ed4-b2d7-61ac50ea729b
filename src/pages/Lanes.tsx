import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import ConfirmationDialog from "@/components/ConfirmationDialog";
import PerformanceTab from "@/components/lanes/PerformanceTab";
import AnalyticsTab from "@/components/lanes/AnalyticsTab";
import { Plus, Download, Upload, Search, Edit, Trash2, Route, MapPin, Truck } from "lucide-react";

const Lanes = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedLane, setSelectedLane] = useState<number | null>(null);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const lanes = [
    {
      id: 1,
      name: "Chicago-Detroit Express",
      origin: "Chicago, IL",
      destination: "Detroit, MI",
      distance: "283 miles",
      status: "Active",
      capacity: "50 tons",
      utilization: 85,
      frequency: "Daily"
    },
    {
      id: 2,
      name: "West Coast Corridor",
      origin: "Los Angeles, CA",
      destination: "San Francisco, CA",
      distance: "382 miles",
      status: "Active",
      capacity: "75 tons",
      utilization: 92,
      frequency: "Twice Daily"
    },
    {
      id: 3,
      name: "Northeast Route",
      origin: "New York, NY",
      destination: "Boston, MA",
      distance: "215 miles",
      status: "Maintenance",
      capacity: "60 tons",
      utilization: 0,
      frequency: "Daily"
    },
    {
      id: 4,
      name: "Texas Triangle",
      origin: "Houston, TX",
      destination: "Dallas, TX",
      distance: "239 miles",
      status: "Active",
      capacity: "80 tons",
      utilization: 78,
      frequency: "Daily"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "bg-green-100 text-green-800";
      case "Maintenance": return "bg-yellow-100 text-yellow-800";
      case "Inactive": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 90) return "text-red-600";
    if (utilization >= 75) return "text-yellow-600";
    return "text-green-600";
  };

  const filteredLanes = lanes.filter(lane =>
    lane.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lane.origin.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lane.destination.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDeleteLane = (laneId: number) => {
    setSelectedLane(laneId);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    console.log(`Deleting lane with ID: ${selectedLane}`);
    setDeleteDialogOpen(false);
    setSelectedLane(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Transportation Lanes</h1>
              <p className="text-gray-600 mt-1">Manage shipping routes and transportation corridors</p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Lane
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Route className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-600">Total Lanes</p>
                    <p className="text-2xl font-bold">{lanes.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Truck className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-600">Active Lanes</p>
                    <p className="text-2xl font-bold">{lanes.filter(l => l.status === "Active").length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="text-sm text-gray-600">Avg Utilization</p>
                    <p className="text-2xl font-bold">
                      {Math.round(lanes.reduce((acc, lane) => acc + lane.utilization, 0) / lanes.length)}%
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Route className="h-5 w-5 text-orange-500" />
                  <div>
                    <p className="text-sm text-gray-600">Total Distance</p>
                    <p className="text-2xl font-bold">
                      {lanes.reduce((acc, lane) => acc + parseInt(lane.distance), 0)} mi
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="lanes" className="space-y-4">
            <TabsList>
              <TabsTrigger value="lanes">Lane Directory</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>
            
            <TabsContent value="lanes" className="space-y-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-4">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="Search lanes by name, origin, or destination..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    <Button variant="outline">
                      Filter
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Transportation Lanes ({filteredLanes.length})</CardTitle>
                  <CardDescription>
                    Manage your transportation corridors and shipping routes
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Lane Name</TableHead>
                        <TableHead>Route</TableHead>
                        <TableHead>Distance</TableHead>
                        <TableHead>Capacity</TableHead>
                        <TableHead>Utilization</TableHead>
                        <TableHead>Frequency</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredLanes.map((lane) => (
                        <TableRow key={lane.id}>
                          <TableCell>
                            <div className="font-medium">{lane.name}</div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="text-sm flex items-center gap-1">
                                <MapPin className="h-3 w-3 text-gray-400" />
                                {lane.origin}
                              </div>
                              <div className="text-sm flex items-center gap-1">
                                <MapPin className="h-3 w-3 text-gray-400" />
                                {lane.destination}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{lane.distance}</TableCell>
                          <TableCell>{lane.capacity}</TableCell>
                          <TableCell>
                            <span className={getUtilizationColor(lane.utilization)}>
                              {lane.utilization}%
                            </span>
                          </TableCell>
                          <TableCell>{lane.frequency}</TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(lane.status)}>
                              {lane.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              <Button variant="outline" size="sm">
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleDeleteLane(lane.id)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <PerformanceTab />
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <AnalyticsTab />
            </TabsContent>
          </Tabs>
        </main>
      </div>

      <ConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Lane"
        description="Are you sure you want to delete this transportation lane? This action cannot be undone."
        confirmLabel="Delete"
        variant="destructive"
        onConfirm={confirmDelete}
      />
    </div>
  );
};

export default Lanes;
