
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Search, Plus, Star, Truck, MapPin, Clock, DollarSign } from "lucide-react";

const Carriers = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const carriers = [
    {
      id: 1,
      name: "Federal Express",
      code: "FDX",
      type: "Express",
      rating: 4.8,
      status: "Active",
      routes: 247,
      avgCost: "$2.45",
      onTimeRate: "98.5%",
      services: ["Ground", "Express", "International"],
      coverage: "National"
    },
    {
      id: 2,
      name: "UPS Logistics",
      code: "UPS",
      type: "Ground",
      rating: 4.6,
      status: "Active",
      routes: 189,
      avgCost: "$2.12",
      onTimeRate: "96.8%",
      services: ["Ground", "Express", "Freight"],
      coverage: "National"
    },
    {
      id: 3,
      name: "DHL Supply Chain",
      code: "DHL",
      type: "International",
      rating: 4.4,
      status: "Active",
      routes: 156,
      avgCost: "$3.21",
      onTimeRate: "94.2%",
      services: ["International", "Express"],
      coverage: "Global"
    },
    {
      id: 4,
      name: "USPS Priority",
      code: "USPS",
      type: "Standard",
      rating: 4.1,
      status: "Active",
      routes: 298,
      avgCost: "$1.87",
      onTimeRate: "92.1%",
      services: ["Ground", "Priority"],
      coverage: "National"
    },
    {
      id: 5,
      name: "Regional Express Co",
      code: "REX",
      type: "Regional",
      rating: 4.3,
      status: "Under Review",
      routes: 78,
      avgCost: "$2.67",
      onTimeRate: "95.4%",
      services: ["Ground", "Express"],
      coverage: "Regional"
    }
  ];

  const filteredCarriers = carriers.filter(carrier =>
    carrier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    carrier.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Under Review":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Express":
        return "bg-blue-100 text-blue-800";
      case "Ground":
        return "bg-purple-100 text-purple-800";
      case "International":
        return "bg-orange-100 text-orange-800";
      case "Regional":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Carriers</h1>
              <p className="text-gray-600 mt-1">Manage your carrier network and partnerships</p>
            </div>
            
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Carrier
            </Button>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-900">47</div>
                    <div className="text-sm text-gray-600">Active Carriers</div>
                  </div>
                  <Truck className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-900">$2.28</div>
                    <div className="text-sm text-gray-600">Avg Cost/Mile</div>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-900">96.2%</div>
                    <div className="text-sm text-gray-600">On-Time Rate</div>
                  </div>
                  <Clock className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-900">968</div>
                    <div className="text-sm text-gray-600">Total Routes</div>
                  </div>
                  <MapPin className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Search and Filters */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search carriers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Carriers Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredCarriers.map((carrier) => (
              <Card key={carrier.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{carrier.name}</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="secondary">{carrier.code}</Badge>
                        <Badge className={getTypeColor(carrier.type)}>{carrier.type}</Badge>
                      </div>
                    </div>
                    <Badge className={getStatusColor(carrier.status)}>{carrier.status}</Badge>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="space-y-4">
                    {/* Rating */}
                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < Math.floor(carrier.rating)
                                ? "text-yellow-400 fill-current"
                                : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-sm font-medium">{carrier.rating}</span>
                    </div>

                    {/* Key Metrics */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-gray-600">Routes</div>
                        <div className="font-semibold">{carrier.routes}</div>
                      </div>
                      <div>
                        <div className="text-gray-600">Avg Cost</div>
                        <div className="font-semibold">{carrier.avgCost}</div>
                      </div>
                      <div>
                        <div className="text-gray-600">On-Time</div>
                        <div className="font-semibold">{carrier.onTimeRate}</div>
                      </div>
                      <div>
                        <div className="text-gray-600">Coverage</div>
                        <div className="font-semibold">{carrier.coverage}</div>
                      </div>
                    </div>

                    {/* Services */}
                    <div>
                      <div className="text-sm text-gray-600 mb-2">Services</div>
                      <div className="flex flex-wrap gap-1">
                        {carrier.services.map((service) => (
                          <Badge key={service} variant="outline" className="text-xs">
                            {service}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        View Details
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        Edit
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Carriers;
