import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import AIInsights from "@/components/AIInsights";
import SmartAnalytics from "@/components/SmartAnalytics";
import AIChatAssistant from "@/components/AIChatAssistant";

const Dashboard = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">AI Analytics Dashboard</h1>
              <p className="text-gray-600 mt-1">Real-time insights and recommendations powered by artificial intelligence</p>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                AI Engine Active
              </div>
              <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border">
                Last analysis: 30 sec ago
              </div>
            </div>
          </div>

          {/* Smart Analytics Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-800">Performance Analytics</h2>
            </div>
            <SmartAnalytics />
          </div>

          {/* AI Insights Section */}
          <div className="space-y-4">
            <AIInsights />
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-blue-600">87%</div>
              <div className="text-sm text-gray-600">AI Confidence Score</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-green-600">23</div>
              <div className="text-sm text-gray-600">Active Insights</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-purple-600">$127K</div>
              <div className="text-sm text-gray-600">Potential Savings</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-orange-600">15</div>
              <div className="text-sm text-gray-600">Recommendations</div>
            </div>
          </div>
        </main>
      </div>

      {/* AI Chat Assistant - Fixed position */}
      <AIChatAssistant />
    </div>
  );
};

export default Dashboard;
