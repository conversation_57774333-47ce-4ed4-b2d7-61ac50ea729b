import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Database, Package, MapPin, Users, Factory, Plus, Download, Upload, Search, Filter, Edit, Trash2, Star } from "lucide-react";
import SupplierDialog, { Supplier } from "@/components/master-data/SupplierDialog";
import SupplierFilters from "@/components/master-data/SupplierFilters";
import ConfirmationDialog from "@/components/ConfirmationDialog";
import { useToast } from "@/hooks/use-toast";

const MasterData = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [suppliers, setSuppliers] = useState<Supplier[]>([
    {
      id: "S001",
      name: "Global Manufacturing Corp",
      category: "Raw Materials",
      region: "North America",
      status: "Active",
      contactEmail: "<EMAIL>",
      contactPhone: "+****************",
      address: "123 Industrial Blvd, Detroit, MI 48201",
      rating: 4.8,
      certifications: ["ISO 9001", "ISO 14001", "OHSAS 18001"]
    },
    {
      id: "S002",
      name: "European Components Ltd",
      category: "Components",
      region: "Europe",
      status: "Active",
      contactEmail: "<EMAIL>",
      contactPhone: "+49 30 12345678",
      address: "Industriestraße 45, 10319 Berlin, Germany",
      rating: 4.6,
      certifications: ["ISO 9001", "CE Marking"]
    },
    {
      id: "S003",
      name: "Asia Pacific Electronics",
      category: "Electronics",
      region: "Asia Pacific",
      status: "Pending",
      contactEmail: "<EMAIL>",
      contactPhone: "+65 6123 4567",
      address: "50 Raffles Place, Singapore 048623",
      rating: 4.2,
      certifications: ["ISO 9001", "RoHS"]
    },
    {
      id: "S004",
      name: "Logistics Solutions Inc",
      category: "Logistics",
      region: "North America",
      status: "Inactive",
      contactEmail: "<EMAIL>",
      contactPhone: "+****************",
      address: "789 Warehouse Ave, Chicago, IL 60601",
      rating: 3.9,
      certifications: ["ISO 27001"]
    }
  ]);

  // Dialog states
  const [supplierDialog, setSupplierDialog] = useState<{
    open: boolean;
    mode: "create" | "edit";
    supplier?: Supplier;
  }>({ open: false, mode: "create" });

  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    supplier?: Supplier;
  }>({ open: false });

  // Filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [regionFilter, setRegionFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");

  const { toast } = useToast();

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Filter logic
  const filteredSuppliers = suppliers.filter(supplier => {
    const matchesSearch = 
      supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.contactEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.address.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = categoryFilter === "all" || supplier.category === categoryFilter;
    const matchesRegion = regionFilter === "all" || supplier.region === regionFilter;
    const matchesStatus = statusFilter === "all" || supplier.status === statusFilter;
    
    return matchesSearch && matchesCategory && matchesRegion && matchesStatus;
  });

  const activeFiltersCount = [categoryFilter, regionFilter, statusFilter].filter(f => f !== "all").length;

  const handleSaveSupplier = (supplierData: Supplier) => {
    if (supplierDialog.mode === "create") {
      setSuppliers(prev => [...prev, supplierData]);
      toast({ title: "Supplier created successfully" });
    } else {
      setSuppliers(prev => prev.map(s => s.id === supplierData.id ? supplierData : s));
      toast({ title: "Supplier updated successfully" });
    }
  };

  const handleDeleteSupplier = () => {
    if (deleteDialog.supplier) {
      setSuppliers(prev => prev.filter(s => s.id !== deleteDialog.supplier!.id));
      toast({ title: "Supplier deleted successfully" });
      setDeleteDialog({ open: false });
    }
  };

  const handleClearFilters = () => {
    setCategoryFilter("all");
    setRegionFilter("all");
    setStatusFilter("all");
    setSearchTerm("");
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "bg-green-100 text-green-800";
      case "Inactive": return "bg-gray-100 text-gray-800";
      case "Pending": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const products = [
    { id: "P001", name: "Product Alpha", category: "Electronics", status: "Active", locations: 12 },
    { id: "P002", name: "Product Beta", category: "Automotive", status: "Active", locations: 8 },
    { id: "P003", name: "Product Gamma", category: "Industrial", status: "Inactive", locations: 5 },
    { id: "P004", name: "Product Delta", category: "Consumer", status: "Active", locations: 15 },
  ];

  const locations = [
    { id: "L001", name: "New York DC", type: "Distribution Center", region: "North America", status: "Active" },
    { id: "L002", name: "Chicago Plant", type: "Manufacturing", region: "North America", status: "Active" },
    { id: "L003", name: "London Warehouse", type: "Warehouse", region: "Europe", status: "Active" },
    { id: "L004", name: "Shanghai Port", type: "Port", region: "Asia Pacific", status: "Under Construction" },
  ];

  const customers = [
    { id: "C001", name: "Global Corp", type: "Enterprise", region: "Global", demand: "High" },
    { id: "C002", name: "Regional Retail", type: "Retail", region: "North America", demand: "Medium" },
    { id: "C003", name: "Tech Solutions", type: "B2B", region: "Europe", demand: "High" },
    { id: "C004", name: "Local Market", type: "SMB", region: "Asia Pacific", demand: "Low" },
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Master Data Management</h1>
              <p className="text-gray-600 mt-1">Manage core business entities and reference data</p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Record
              </Button>
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Package className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-600">Products</p>
                    <p className="text-2xl font-bold">1,247</p>
                    <p className="text-sm text-green-600">+12 this month</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-600">Locations</p>
                    <p className="text-2xl font-bold">156</p>
                    <p className="text-sm text-gray-600">Across 24 countries</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="text-sm text-gray-600">Customers</p>
                    <p className="text-2xl font-bold">8,432</p>
                    <p className="text-sm text-blue-600">+156 new</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Factory className="h-5 w-5 text-orange-500" />
                  <div>
                    <p className="text-sm text-gray-600">Suppliers</p>
                    <p className="text-2xl font-bold">{suppliers.length}</p>
                    <p className="text-sm text-gray-600">Active partners</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <Tabs defaultValue="products" className="space-y-4">
            <TabsList>
              <TabsTrigger value="products">Products</TabsTrigger>
              <TabsTrigger value="locations">Locations</TabsTrigger>
              <TabsTrigger value="customers">Customers</TabsTrigger>
              <TabsTrigger value="suppliers">Suppliers</TabsTrigger>
            </TabsList>
            
            <TabsContent value="products" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Product Master</CardTitle>
                      <CardDescription>Manage product catalog and attributes</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Search className="h-4 w-4 mr-2" />
                        Search
                      </Button>
                      <Button variant="outline" size="sm">
                        <Filter className="h-4 w-4 mr-2" />
                        Filter
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-3">Product ID</th>
                          <th className="text-left p-3">Name</th>
                          <th className="text-left p-3">Category</th>
                          <th className="text-left p-3">Status</th>
                          <th className="text-right p-3">Locations</th>
                          <th className="text-left p-3">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {products.map((product) => (
                          <tr key={product.id} className="border-b hover:bg-gray-50">
                            <td className="p-3 font-mono text-sm">{product.id}</td>
                            <td className="p-3 font-medium">{product.name}</td>
                            <td className="p-3">{product.category}</td>
                            <td className="p-3">
                              <Badge className={getStatusColor(product.status)}>
                                {product.status}
                              </Badge>
                            </td>
                            <td className="p-3 text-right">{product.locations}</td>
                            <td className="p-3">
                              <Button variant="outline" size="sm">
                                Edit
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="locations" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Location Master</CardTitle>
                      <CardDescription>Manage facilities, warehouses, and distribution centers</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Search className="h-4 w-4 mr-2" />
                        Search
                      </Button>
                      <Button variant="outline" size="sm">
                        <Filter className="h-4 w-4 mr-2" />
                        Filter
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-3">Location ID</th>
                          <th className="text-left p-3">Name</th>
                          <th className="text-left p-3">Type</th>
                          <th className="text-left p-3">Region</th>
                          <th className="text-left p-3">Status</th>
                          <th className="text-left p-3">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {locations.map((location) => (
                          <tr key={location.id} className="border-b hover:bg-gray-50">
                            <td className="p-3 font-mono text-sm">{location.id}</td>
                            <td className="p-3 font-medium">{location.name}</td>
                            <td className="p-3">{location.type}</td>
                            <td className="p-3">{location.region}</td>
                            <td className="p-3">
                              <Badge className={getStatusColor(location.status)}>
                                {location.status}
                              </Badge>
                            </td>
                            <td className="p-3">
                              <Button variant="outline" size="sm">
                                Edit
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="customers" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Customer Master</CardTitle>
                      <CardDescription>Manage customer data and relationships</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Search className="h-4 w-4 mr-2" />
                        Search
                      </Button>
                      <Button variant="outline" size="sm">
                        <Filter className="h-4 w-4 mr-2" />
                        Filter
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-3">Customer ID</th>
                          <th className="text-left p-3">Name</th>
                          <th className="text-left p-3">Type</th>
                          <th className="text-left p-3">Region</th>
                          <th className="text-left p-3">Demand Level</th>
                          <th className="text-left p-3">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {customers.map((customer) => (
                          <tr key={customer.id} className="border-b hover:bg-gray-50">
                            <td className="p-3 font-mono text-sm">{customer.id}</td>
                            <td className="p-3 font-medium">{customer.name}</td>
                            <td className="p-3">{customer.type}</td>
                            <td className="p-3">{customer.region}</td>
                            <td className="p-3">
                              <Badge className={
                                customer.demand === "High" ? "bg-red-100 text-red-800" :
                                customer.demand === "Medium" ? "bg-yellow-100 text-yellow-800" :
                                "bg-green-100 text-green-800"
                              }>
                                {customer.demand}
                              </Badge>
                            </td>
                            <td className="p-3">
                              <Button variant="outline" size="sm">
                                Edit
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="suppliers" className="space-y-4">
              {/* Supplier Filters */}
              <SupplierFilters
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                categoryFilter={categoryFilter}
                setCategoryFilter={setCategoryFilter}
                regionFilter={regionFilter}
                setRegionFilter={setRegionFilter}
                statusFilter={statusFilter}
                setStatusFilter={setStatusFilter}
                onAddSupplier={() => setSupplierDialog({ open: true, mode: "create" })}
                onClearFilters={handleClearFilters}
                activeFiltersCount={activeFiltersCount}
              />

              {/* Suppliers Table */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Supplier Master ({filteredSuppliers.length})</CardTitle>
                      <CardDescription>Manage supplier information and partnerships</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-3">Supplier ID</th>
                          <th className="text-left p-3">Name</th>
                          <th className="text-left p-3">Category</th>
                          <th className="text-left p-3">Region</th>
                          <th className="text-left p-3">Status</th>
                          <th className="text-left p-3">Contact</th>
                          <th className="text-left p-3">Rating</th>
                          <th className="text-left p-3">Certifications</th>
                          <th className="text-left p-3">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredSuppliers.map((supplier) => (
                          <tr key={supplier.id} className="border-b hover:bg-gray-50">
                            <td className="p-3 font-mono text-sm">{supplier.id}</td>
                            <td className="p-3">
                              <div>
                                <div className="font-medium">{supplier.name}</div>
                                <div className="text-sm text-gray-500">{supplier.address}</div>
                              </div>
                            </td>
                            <td className="p-3">{supplier.category}</td>
                            <td className="p-3">{supplier.region}</td>
                            <td className="p-3">
                              <Badge className={getStatusColor(supplier.status)}>
                                {supplier.status}
                              </Badge>
                            </td>
                            <td className="p-3">
                              <div className="text-sm">
                                <div>{supplier.contactEmail}</div>
                                <div className="text-gray-500">{supplier.contactPhone}</div>
                              </div>
                            </td>
                            <td className="p-3">
                              <div className="flex items-center gap-1">
                                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                <span className="font-medium">{supplier.rating}</span>
                              </div>
                            </td>
                            <td className="p-3">
                              <div className="flex flex-wrap gap-1">
                                {supplier.certifications.slice(0, 2).map((cert, index) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {cert}
                                  </Badge>
                                ))}
                                {supplier.certifications.length > 2 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{supplier.certifications.length - 2}
                                  </Badge>
                                )}
                              </div>
                            </td>
                            <td className="p-3">
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setSupplierDialog({ open: true, mode: "edit", supplier })}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setDeleteDialog({ open: true, supplier })}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  
                  {filteredSuppliers.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      No suppliers found matching your criteria.
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>

      {/* Supplier Dialog */}
      <SupplierDialog
        open={supplierDialog.open}
        onOpenChange={(open) => setSupplierDialog(prev => ({ ...prev, open }))}
        mode={supplierDialog.mode}
        supplier={supplierDialog.supplier}
        onSave={handleSaveSupplier}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog(prev => ({ ...prev, open }))}
        title="Delete Supplier"
        description={`Are you sure you want to delete "${deleteDialog.supplier?.name}"? This action cannot be undone.`}
        confirmLabel="Delete"
        variant="destructive"
        onConfirm={handleDeleteSupplier}
      />
    </div>
  );
};

export default MasterData;
