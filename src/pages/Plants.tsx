
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Factory, Users, Zap, TrendingUp, Plus, Download, Upload, Search, Filter, Settings } from "lucide-react";
import PlantMetrics from "@/components/plants/PlantMetrics";
import PlantChart from "@/components/plants/PlantChart";
import { mockPlants, mockPlantPerformance, calculatePlantMetrics, getStatusColor, getEnergyRatingColor } from "@/utils/plantUtils";

const Plants = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const metrics = calculatePlantMetrics(mockPlants);

  const utilizationData = mockPlants.map(plant => ({
    name: plant.name.split(' ')[0],
    utilization: plant.utilization,
    capacity: plant.capacity
  }));

  const performanceData = mockPlantPerformance.map(perf => {
    const plant = mockPlants.find(p => p.id === perf.plantId);
    return {
      name: plant?.name.split(' ')[0] || perf.plantId,
      efficiency: perf.efficiency,
      quality: perf.qualityScore,
      safety: perf.safetyScore
    };
  });

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Plants Management</h1>
              <p className="text-gray-600 mt-1">Monitor and manage manufacturing facilities</p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Plant
              </Button>
            </div>
          </div>

          {/* Summary Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <PlantMetrics
              icon={Factory}
              iconColor="text-blue-500"
              label="Total Plants"
              value={metrics.totalPlants}
              change={{ value: 12, isPositive: true }}
            />
            <PlantMetrics
              icon={Zap}
              iconColor="text-green-500"
              label="Active Plants"
              value={metrics.activeBlants}
              change={{ value: 5, isPositive: true }}
            />
            <PlantMetrics
              icon={TrendingUp}
              iconColor="text-purple-500"
              label="Avg Utilization"
              value={`${Math.round(metrics.averageUtilization)}%`}
              change={{ value: 3, isPositive: true }}
            />
            <PlantMetrics
              icon={Users}
              iconColor="text-orange-500"
              label="Total Employees"
              value={metrics.totalEmployees.toLocaleString()}
              change={{ value: 8, isPositive: true }}
            />
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <PlantChart
              title="Plant Utilization"
              description="Current capacity utilization across plants"
              data={utilizationData}
              type="bar"
              dataKey="utilization"
              xAxisKey="name"
              color="#3b82f6"
            />
            <PlantChart
              title="Performance Metrics"
              description="Efficiency trends over time"
              data={performanceData}
              type="line"
              dataKey="efficiency"
              xAxisKey="name"
              color="#10b981"
            />
          </div>

          {/* Main Content */}
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Plant Overview</CardTitle>
                      <CardDescription>Complete list of manufacturing facilities</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Search className="h-4 w-4 mr-2" />
                        Search
                      </Button>
                      <Button variant="outline" size="sm">
                        <Filter className="h-4 w-4 mr-2" />
                        Filter
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-3">Plant ID</th>
                          <th className="text-left p-3">Name</th>
                          <th className="text-left p-3">Location</th>
                          <th className="text-left p-3">Type</th>
                          <th className="text-left p-3">Status</th>
                          <th className="text-right p-3">Utilization</th>
                          <th className="text-right p-3">Employees</th>
                          <th className="text-left p-3">Manager</th>
                          <th className="text-left p-3">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {mockPlants.map((plant) => (
                          <tr key={plant.id} className="border-b hover:bg-gray-50">
                            <td className="p-3 font-mono text-sm">{plant.id}</td>
                            <td className="p-3 font-medium">{plant.name}</td>
                            <td className="p-3">{plant.location}</td>
                            <td className="p-3">{plant.type}</td>
                            <td className="p-3">
                              <Badge className={getStatusColor(plant.status)}>
                                {plant.status}
                              </Badge>
                            </td>
                            <td className="p-3 text-right">{plant.utilization}%</td>
                            <td className="p-3 text-right">{plant.employees}</td>
                            <td className="p-3">{plant.manager}</td>
                            <td className="p-3">
                              <Button variant="outline" size="sm">
                                <Settings className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Analytics</CardTitle>
                  <CardDescription>Key performance indicators for all plants</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-3">Plant</th>
                          <th className="text-right p-3">Efficiency</th>
                          <th className="text-right p-3">Quality Score</th>
                          <th className="text-right p-3">Safety Score</th>
                          <th className="text-right p-3">On-Time Delivery</th>
                          <th className="text-right p-3">Cost/Unit</th>
                          <th className="text-right p-3">Downtime</th>
                        </tr>
                      </thead>
                      <tbody>
                        {mockPlantPerformance.map((perf) => {
                          const plant = mockPlants.find(p => p.id === perf.plantId);
                          return (
                            <tr key={perf.plantId} className="border-b hover:bg-gray-50">
                              <td className="p-3 font-medium">{plant?.name}</td>
                              <td className="p-3 text-right">{perf.efficiency}%</td>
                              <td className="p-3 text-right">{perf.qualityScore}%</td>
                              <td className="p-3 text-right">{perf.safetyScore}%</td>
                              <td className="p-3 text-right">{perf.onTimeDelivery}%</td>
                              <td className="p-3 text-right">${perf.costPerUnit}</td>
                              <td className="p-3 text-right">{perf.downtime}%</td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="maintenance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Maintenance Schedule</CardTitle>
                  <CardDescription>Planned and ongoing maintenance activities</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockPlants.map((plant) => (
                      <div key={plant.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">{plant.name}</p>
                          <p className="text-sm text-gray-600">{plant.location}</p>
                        </div>
                        <div className="text-right">
                          <Badge className={getStatusColor(plant.status)}>
                            {plant.status}
                          </Badge>
                          {plant.status === "Maintenance" && (
                            <p className="text-sm text-gray-600 mt-1">Scheduled maintenance</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Energy Efficiency</CardTitle>
                    <CardDescription>Energy ratings across facilities</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {mockPlants.map((plant) => (
                        <div key={plant.id} className="flex items-center justify-between">
                          <span className="text-sm">{plant.name}</span>
                          <Badge className={getEnergyRatingColor(plant.energyRating)}>
                            Rating {plant.energyRating}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Capacity Analysis</CardTitle>
                    <CardDescription>Capacity vs utilization breakdown</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {mockPlants.map((plant) => (
                        <div key={plant.id} className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span>{plant.name}</span>
                            <span>{plant.utilization}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-500 h-2 rounded-full" 
                              style={{ width: `${plant.utilization}%` }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default Plants;
