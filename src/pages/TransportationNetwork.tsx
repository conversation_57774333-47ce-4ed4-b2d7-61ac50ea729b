
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import TransportationMap from "@/components/TransportationMap";
import NetworkFilters from "@/components/NetworkFilters";
import NetworkStats from "@/components/NetworkStats";

const TransportationNetwork = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Transportation Network</h1>
              <p className="text-gray-600 mt-1">Visualize and optimize your logistics network with route analysis</p>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                Network Active
              </div>
              <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border">
                Last sync: 3 min ago
              </div>
            </div>
          </div>

          {/* Network Statistics */}
          <NetworkStats />

          {/* Map and Filters Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Filters Panel */}
            <div className="lg:col-span-1">
              <NetworkFilters />
            </div>
            
            {/* Map Visualization */}
            <div className="lg:col-span-3">
              <TransportationMap />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default TransportationNetwork;
