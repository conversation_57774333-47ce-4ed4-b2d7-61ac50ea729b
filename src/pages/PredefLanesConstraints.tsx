
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import ConfirmationDialog from "@/components/ConfirmationDialog";
import { Plus, Download, Upload, Link, Route, Settings, AlertTriangle } from "lucide-react";
import { laneConstraints, overrideRules, constraintTemplates } from "@/data/laneConstraintData";
import MetricsCard from "@/components/constraints/MetricsCard";
import LaneMappingsTab from "@/components/constraints/LaneMappingsTab";
import OverrideRulesTab from "@/components/constraints/OverrideRulesTab";
import TemplatesTab from "@/components/constraints/TemplatesTab";

const PredefLanesConstraints = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedMapping, setSelectedMapping] = useState<number | null>(null);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handleDeleteMapping = (mappingId: number) => {
    setSelectedMapping(mappingId);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    console.log(`Deleting lane constraint mapping with ID: ${selectedMapping}`);
    setDeleteDialogOpen(false);
    setSelectedMapping(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Predefined Lane Constraints</h1>
              <p className="text-gray-600 mt-1">Map constraints to specific transportation lanes</p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Map Constraint
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <MetricsCard
              icon={Link}
              iconColor="text-blue-500"
              label="Total Mappings"
              value={laneConstraints.length}
            />
            <MetricsCard
              icon={Route}
              iconColor="text-green-500"
              label="Active Mappings"
              value={laneConstraints.filter(m => m.isActive).length}
            />
            <MetricsCard
              icon={Settings}
              iconColor="text-purple-500"
              label="With Overrides"
              value={laneConstraints.filter(m => m.overrideValue).length}
            />
            <MetricsCard
              icon={AlertTriangle}
              iconColor="text-orange-500"
              label="Constraint Types"
              value={new Set(laneConstraints.map(m => m.constraintType)).size}
            />
          </div>

          <Tabs defaultValue="mappings" className="space-y-4">
            <TabsList>
              <TabsTrigger value="mappings">Lane-Constraint Mappings</TabsTrigger>
              <TabsTrigger value="overrides">Override Rules</TabsTrigger>
              <TabsTrigger value="templates">Templates</TabsTrigger>
            </TabsList>
            
            <TabsContent value="mappings" className="space-y-4">
              <LaneMappingsTab
                mappings={laneConstraints}
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                onDelete={handleDeleteMapping}
              />
            </TabsContent>

            <TabsContent value="overrides" className="space-y-4">
              <OverrideRulesTab
                overrideRules={overrideRules}
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
              />
            </TabsContent>

            <TabsContent value="templates" className="space-y-4">
              <TemplatesTab
                templates={constraintTemplates}
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
              />
            </TabsContent>
          </Tabs>
        </main>
      </div>

      <ConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Remove Constraint Mapping"
        description="Are you sure you want to remove this constraint mapping? This action cannot be undone."
        confirmLabel="Remove"
        variant="destructive"
        onConfirm={confirmDelete}
      />
    </div>
  );
};

export default PredefLanesConstraints;
