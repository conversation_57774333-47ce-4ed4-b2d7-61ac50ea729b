
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import MapContainer from "@/components/MapContainer";
import AIChatAssistant from "@/components/AIChatAssistant";

const Index = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Center of Gravity Analysis</h1>
              <p className="text-gray-600 mt-1">AI-powered logistics optimization with real-time ERP integration</p>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                AI Assistant Active
              </div>
              <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border">
                Last updated: 2 min ago
              </div>
            </div>
          </div>

          {/* Map Section - Full width */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-800">Center of Gravity Visualization</h2>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                Live COG Data
              </div>
            </div>
            <MapContainer />
          </div>

          {/* COG Analysis Results */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-800">Analysis Results</h2>
              <div className="flex items-center gap-2">
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  Run Analysis
                </button>
                <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                  Export Results
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Current COG */}
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h3 className="text-lg font-semibold mb-4">Current Center of Gravity</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Latitude:</span>
                    <span className="font-mono">39.8283° N</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Longitude:</span>
                    <span className="font-mono">-98.5795° W</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Location:</span>
                    <span>Kansas, USA</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Distance:</span>
                    <span className="font-semibold">2,847 miles</span>
                  </div>
                </div>
              </div>

              {/* Optimal COG */}
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h3 className="text-lg font-semibold mb-4">Recommended Center of Gravity</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Latitude:</span>
                    <span className="font-mono">39.7391° N</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Longitude:</span>
                    <span className="font-mono">-104.9903° W</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Location:</span>
                    <span>Denver, CO</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Distance:</span>
                    <span className="font-semibold text-green-600">2,156 miles</span>
                  </div>
                  <div className="mt-4 p-3 bg-green-50 rounded-lg">
                    <div className="text-sm text-green-800">
                      <strong>Improvement:</strong> 691 miles reduction (24.3%)
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* COG Analysis Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-blue-600">24</div>
              <div className="text-sm text-gray-600">Distribution Points</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-green-600">156.2</div>
              <div className="text-sm text-gray-600">Avg Distance (mi)</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-purple-600">$42.5K</div>
              <div className="text-sm text-gray-600">Annual Savings</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-orange-600">24.3%</div>
              <div className="text-sm text-gray-600">Distance Reduction</div>
            </div>
          </div>
        </main>
      </div>

      {/* AI Chat Assistant - Fixed position */}
      <AIChatAssistant />
    </div>
  );
};

export default Index;
