
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import MapContainer from "@/components/MapContainer";
import DataTable from "@/components/DataTable";
import AIInsights from "@/components/AIInsights";
import AIChatAssistant from "@/components/AIChatAssistant";
import SmartAnalytics from "@/components/SmartAnalytics";

const Index = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Center of Gravity Analysis</h1>
              <p className="text-gray-600 mt-1">AI-powered logistics optimization with real-time ERP integration</p>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                AI Assistant Active
              </div>
              <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border">
                Last updated: 2 min ago
              </div>
            </div>
          </div>

          {/* Smart Analytics Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-800">AI-Powered Analytics</h2>
            </div>
            <SmartAnalytics />
          </div>

          {/* Map Section - Full width */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-800">Network Visualization</h2>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                Live Data
              </div>
            </div>
            <MapContainer />
          </div>

          {/* AI Insights Section - Full width */}
          <div className="space-y-4">
            <AIInsights />
          </div>

          {/* Data Table Section */}
          <div className="space-y-4">
            <DataTable />
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-blue-600">24</div>
              <div className="text-sm text-gray-600">Active Locations</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-green-600">156.2</div>
              <div className="text-sm text-gray-600">Avg Distance (mi)</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-purple-600">$42.5K</div>
              <div className="text-sm text-gray-600">Est. Cost Savings</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-orange-600">98.2%</div>
              <div className="text-sm text-gray-600">Optimization Score</div>
            </div>
          </div>
        </main>
      </div>

      {/* AI Chat Assistant - Fixed position */}
      <AIChatAssistant />
    </div>
  );
};

export default Index;
