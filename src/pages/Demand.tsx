import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { TrendingUp, BarChart3, Plus, Download, Upload, Target, AlertCircle, Copy, Trash2, Edit, Eye } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Pie<PERSON><PERSON>, <PERSON>, <PERSON> } from "recharts";
import NewForecastDialog from "@/components/demand/NewForecastDialog";
import EditScenarioDialog from "@/components/demand/EditScenarioDialog";
import ScenarioDetailsDialog from "@/components/demand/ScenarioDetailsDialog";
import ConfirmationDialog from "@/components/ConfirmationDialog";
import ImportExportUtils from "@/components/shared/ImportExportUtils";

const Demand = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [demandData, setDemandData] = useState([
    { id: 1, product: "Product A", region: "North America", forecast: 15420, actual: 14850, variance: -3.7, status: "On Track" },
    { id: 2, product: "Product B", region: "Europe", forecast: 8760, actual: 9120, variance: 4.1, status: "Above Target" },
    { id: 3, product: "Product C", region: "Asia Pacific", forecast: 12300, actual: 11200, variance: -8.9, status: "Below Target" },
    { id: 4, product: "Product D", region: "Latin America", forecast: 5600, actual: 5890, variance: 5.2, status: "Above Target" },
  ]);
  
  const [scenarios, setScenarios] = useState([
    { id: 1, name: "Baseline Forecast", description: "Current market conditions", status: "Active", accuracy: 94.2 },
    { id: 2, name: "Optimistic Growth", description: "15% market expansion", status: "Draft", accuracy: 89.5 },
    { id: 3, name: "Economic Downturn", description: "Conservative estimates", status: "Draft", accuracy: 96.1 },
  ]);

  // Dialog states
  const [editScenarioOpen, setEditScenarioOpen] = useState(false);
  const [scenarioDetailsOpen, setScenarioDetailsOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [selectedScenario, setSelectedScenario] = useState(null);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handleForecastCreate = (newForecast: any) => {
    setDemandData([...demandData, newForecast]);
  };

  const handleForecastsImport = (importedForecasts: any[]) => {
    setDemandData([...demandData, ...importedForecasts]);
  };

  const handleScenarioUpdate = (updatedScenario: any) => {
    setScenarios(scenarios.map(scenario => 
      scenario.id === updatedScenario.id ? updatedScenario : scenario
    ));
  };

  const handleEditScenario = (scenario: any) => {
    setSelectedScenario(scenario);
    setEditScenarioOpen(true);
  };

  const handleViewScenario = (scenario: any) => {
    setSelectedScenario(scenario);
    setScenarioDetailsOpen(true);
  };

  const handleDeleteScenario = (scenario: any) => {
    setSelectedScenario(scenario);
    setDeleteConfirmOpen(true);
  };

  const confirmDeleteScenario = () => {
    if (selectedScenario) {
      setScenarios(scenarios.filter(s => s.id !== selectedScenario.id));
      setSelectedScenario(null);
    }
  };

  const duplicateScenario = (id: number) => {
    const scenario = scenarios.find(s => s.id === id);
    if (scenario) {
      const newScenario = {
        ...scenario,
        id: Math.max(...scenarios.map(s => s.id)) + 1,
        name: `${scenario.name} (Copy)`,
        status: "Draft"
      };
      setScenarios([...scenarios, newScenario]);
    }
  };

  const trendData = [
    { month: "Jan", forecast: 42000, actual: 40500 },
    { month: "Feb", forecast: 43200, actual: 42800 },
    { month: "Mar", forecast: 44100, actual: 43900 },
    { month: "Apr", forecast: 45300, actual: 44200 },
    { month: "May", forecast: 46800, actual: 47100 },
    { month: "Jun", forecast: 48200, actual: 47800 },
  ];

  const regionData = [
    { region: "North America", value: 35, color: "#3b82f6" },
    { region: "Europe", value: 28, color: "#10b981" },
    { region: "Asia Pacific", value: 25, color: "#f59e0b" },
    { region: "Latin America", value: 12, color: "#ef4444" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "On Track": return "bg-green-100 text-green-800";
      case "Above Target": return "bg-blue-100 text-blue-800";
      case "Below Target": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getVarianceColor = (variance: number) => {
    if (variance > 0) return "text-green-600";
    if (variance < -5) return "text-red-600";
    return "text-yellow-600";
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Demand Management</h1>
              <p className="text-gray-600 mt-1">Forecast, analyze, and manage demand across your network</p>
            </div>
            
            <div className="flex items-center gap-3">
              <ImportExportUtils 
                data={demandData} 
                filename="demand-forecasts" 
                onImport={handleForecastsImport}
              />
              <NewForecastDialog onForecastCreate={handleForecastCreate} />
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-600">Total Forecast</p>
                    <p className="text-2xl font-bold">42.08K</p>
                    <p className="text-sm text-green-600">+2.4% vs last period</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-600">Forecast Accuracy</p>
                    <p className="text-2xl font-bold">94.2%</p>
                    <p className="text-sm text-green-600">+1.2% improvement</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="text-sm text-gray-600">Active Forecasts</p>
                    <p className="text-2xl font-bold">24</p>
                    <p className="text-sm text-gray-600">Across 4 regions</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-orange-500" />
                  <div>
                    <p className="text-sm text-gray-600">Exceptions</p>
                    <p className="text-2xl font-bold">3</p>
                    <p className="text-sm text-red-600">Require attention</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <Tabs defaultValue="forecast" className="space-y-4">
            <TabsList>
              <TabsTrigger value="forecast">Demand Forecast</TabsTrigger>
              <TabsTrigger value="analysis">Analysis</TabsTrigger>
              <TabsTrigger value="scenarios">Scenarios</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            
            <TabsContent value="forecast" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Demand Forecast Overview</CardTitle>
                  <CardDescription>
                    Current period demand forecasts vs actual performance
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-3">Product</th>
                          <th className="text-left p-3">Region</th>
                          <th className="text-right p-3">Forecast</th>
                          <th className="text-right p-3">Actual</th>
                          <th className="text-right p-3">Variance %</th>
                          <th className="text-left p-3">Status</th>
                          <th className="text-left p-3">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {demandData.map((item) => (
                          <tr key={item.id} className="border-b hover:bg-gray-50">
                            <td className="p-3 font-medium">{item.product}</td>
                            <td className="p-3">{item.region}</td>
                            <td className="p-3 text-right">{item.forecast.toLocaleString()}</td>
                            <td className="p-3 text-right">{item.actual.toLocaleString()}</td>
                            <td className={`p-3 text-right font-medium ${getVarianceColor(item.variance)}`}>
                              {item.variance > 0 ? '+' : ''}{item.variance}%
                            </td>
                            <td className="p-3">
                              <Badge className={getStatusColor(item.status)}>
                                {item.status}
                              </Badge>
                            </td>
                            <td className="p-3">
                              <Button variant="outline" size="sm">
                                View Details
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analysis" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Demand Trends</CardTitle>
                    <CardDescription>Historical and forecasted demand patterns</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={trendData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Line type="monotone" dataKey="forecast" stroke="#3b82f6" strokeWidth={2} name="Forecast" />
                        <Line type="monotone" dataKey="actual" stroke="#10b981" strokeWidth={2} name="Actual" />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Regional Distribution</CardTitle>
                    <CardDescription>Demand breakdown by region</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={regionData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={100}
                          dataKey="value"
                          label={({ region, value }) => `${region}: ${value}%`}
                        >
                          {regionData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle>Product Performance Analysis</CardTitle>
                    <CardDescription>Forecast accuracy by product category</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={demandData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="product" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="forecast" fill="#3b82f6" name="Forecast" />
                        <Bar dataKey="actual" fill="#10b981" name="Actual" />
                      </BarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="scenarios" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Demand Scenarios</CardTitle>
                      <CardDescription>
                        Create and compare different demand scenarios
                      </CardDescription>
                    </div>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Scenario
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {scenarios.map((scenario) => (
                      <div key={scenario.id} className="border rounded-lg p-4 space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-semibold">{scenario.name}</h3>
                            <p className="text-sm text-gray-600">{scenario.description}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={scenario.status === "Active" ? "default" : "secondary"}>
                              {scenario.status}
                            </Badge>
                            <div className="flex gap-1">
                              <Button 
                                variant="outline" 
                                size="sm" 
                                onClick={() => handleViewScenario(scenario)}
                                title="View Details"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="outline" 
                                size="sm" 
                                onClick={() => duplicateScenario(scenario.id)}
                                title="Copy Scenario"
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleEditScenario(scenario)}
                                title="Edit Scenario"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              {scenario.status !== "Active" && (
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  onClick={() => handleDeleteScenario(scenario)}
                                  title="Delete Scenario"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600">Forecast Accuracy</p>
                            <p className="font-medium">{scenario.accuracy}%</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Time Horizon</p>
                            <p className="font-medium">12 months</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Last Updated</p>
                            <p className="font-medium">2 days ago</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Forecast Configuration</CardTitle>
                    <CardDescription>
                      Configure demand forecasting parameters
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="horizon">Forecast Horizon</Label>
                      <Select defaultValue="12">
                        <SelectTrigger>
                          <SelectValue placeholder="Select horizon" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="3">3 Months</SelectItem>
                          <SelectItem value="6">6 Months</SelectItem>
                          <SelectItem value="12">12 Months</SelectItem>
                          <SelectItem value="24">24 Months</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="method">Forecast Method</Label>
                      <Select defaultValue="ml">
                        <SelectTrigger>
                          <SelectValue placeholder="Select method" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ml">Machine Learning</SelectItem>
                          <SelectItem value="statistical">Statistical</SelectItem>
                          <SelectItem value="manual">Manual</SelectItem>
                          <SelectItem value="hybrid">Hybrid</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="accuracy">Minimum Accuracy Threshold (%)</Label>
                      <Input 
                        id="accuracy" 
                        type="number" 
                        defaultValue="85" 
                        min="0" 
                        max="100" 
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="update-frequency">Update Frequency</Label>
                      <Select defaultValue="weekly">
                        <SelectTrigger>
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Alert Settings</CardTitle>
                    <CardDescription>
                      Configure notifications and alerts
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Forecast Accuracy Alerts</Label>
                        <p className="text-sm text-gray-600">
                          Alert when accuracy drops below threshold
                        </p>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Demand Spike Detection</Label>
                        <p className="text-sm text-gray-600">
                          Alert for unexpected demand changes
                        </p>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Weekly Summary Reports</Label>
                        <p className="text-sm text-gray-600">
                          Email weekly performance summaries
                        </p>
                      </div>
                      <Switch />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="variance-threshold">Variance Alert Threshold (%)</Label>
                      <Input 
                        id="variance-threshold" 
                        type="number" 
                        defaultValue="10" 
                        min="1" 
                        max="50" 
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="notification-email">Notification Email</Label>
                      <Input 
                        id="notification-email" 
                        type="email" 
                        placeholder="<EMAIL>" 
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle>Data Sources</CardTitle>
                    <CardDescription>
                      Configure external data integrations
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">ERP System</h4>
                          <Switch defaultChecked />
                        </div>
                        <p className="text-sm text-gray-600">Historical sales data</p>
                        <p className="text-xs text-green-600">Connected</p>
                      </div>
                      
                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">Market Data API</h4>
                          <Switch />
                        </div>
                        <p className="text-sm text-gray-600">External market trends</p>
                        <p className="text-xs text-gray-500">Not configured</p>
                      </div>
                      
                      <div className="border rounded-lg p-4 space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">Weather Data</h4>
                          <Switch defaultChecked />
                        </div>
                        <p className="text-sm text-gray-600">Seasonal adjustments</p>
                        <p className="text-xs text-green-600">Connected</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="flex justify-end gap-3">
                <Button variant="outline">Reset to Defaults</Button>
                <Button>Save Settings</Button>
              </div>
            </TabsContent>
          </Tabs>
        </main>
      </div>

      {/* Dialogs */}
      <EditScenarioDialog
        open={editScenarioOpen}
        onOpenChange={setEditScenarioOpen}
        scenario={selectedScenario}
        onSave={handleScenarioUpdate}
      />

      <ScenarioDetailsDialog
        open={scenarioDetailsOpen}
        onOpenChange={setScenarioDetailsOpen}
        scenario={selectedScenario}
      />

      <ConfirmationDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        title="Delete Scenario"
        description={`Are you sure you want to delete "${selectedScenario?.name}"? This action cannot be undone.`}
        confirmLabel="Delete"
        variant="destructive"
        onConfirm={confirmDeleteScenario}
      />
    </div>
  );
};

export default Demand;

</edits_to_apply>
