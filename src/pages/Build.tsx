import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  Settings, 
  Network, 
  Zap, 
  TestTube, 
  Monitor,
  Factory,
  Truck,
  MapPin,
  Link,
  AlertTriangle,
  Target,
  Activity,
  Database,
  Shield,
  BarChart3,
  Cpu,
  HardDrive,
  Wifi
} from "lucide-react";

const Build = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const infrastructureModules = [
    {
      title: "Network Design",
      icon: Network,
      description: "Configure suppliers, plants, warehouses, and transportation networks",
      items: ["Suppliers", "Plants & Facilities", "Warehouses", "Transportation"],
      status: "active",
      color: "bg-blue-50 border-blue-200 text-blue-800"
    },
    {
      title: "Connections",
      icon: Link,
      description: "Manage carriers, lanes, constraints, and integration points",
      items: ["Carriers", "Lanes", "Constraints", "Integrations"],
      status: "active", 
      color: "bg-green-50 border-green-200 text-green-800"
    },
    {
      title: "Optimization Engine",
      icon: Zap,
      description: "Configure optimization models, solvers, and algorithm parameters",
      items: ["Model Parameters", "Solver Config", "Algorithm Tuning", "Performance"],
      status: "active",
      color: "bg-purple-50 border-purple-200 text-purple-800"
    },
    {
      title: "Simulation Lab",
      icon: TestTube,
      description: "Run scenario simulations and what-if analysis",
      items: ["Scenario Planning", "What-If Analysis", "Load Testing", "Benchmarks"],
      status: "beta",
      color: "bg-orange-50 border-orange-200 text-orange-800"
    },
    {
      title: "System Config",
      icon: Settings,
      description: "Environment settings, APIs, security, and monitoring",
      items: ["Environment", "API Config", "Security", "Monitoring"],
      status: "active",
      color: "bg-gray-50 border-gray-200 text-gray-800"
    },
    {
      title: "Engineering Analytics",
      icon: BarChart3,
      description: "System performance, configuration audit, and health monitoring",
      items: ["Performance", "Audit Trail", "Usage Metrics", "Health Check"],
      status: "active",
      color: "bg-indigo-50 border-indigo-200 text-indigo-800"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                  <Settings className="h-6 w-6 text-white" />
                </div>
                Infrastructure Engineering
              </h1>
              <p className="text-gray-600 mt-1">Configure, optimize, and monitor your supply chain infrastructure</p>
            </div>
            
            <div className="flex items-center gap-3">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                Engineering Mode
              </Badge>
              <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border">
                System Health: 98.7%
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Configurations</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">247</div>
                <p className="text-xs text-muted-foreground">+12 this week</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">99.9%</div>
                <p className="text-xs text-muted-foreground">Last 30 days</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">API Calls</CardTitle>
                <Wifi className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1.2M</div>
                <p className="text-xs text-muted-foreground">+8.3% from last month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Optimization Jobs</CardTitle>
                <Cpu className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,847</div>
                <p className="text-xs text-muted-foreground">This month</p>
              </CardContent>
            </Card>
          </div>

          {/* Infrastructure Modules */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-800">Infrastructure Modules</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {infrastructureModules.map((module, index) => {
                const Icon = module.icon;
                return (
                  <Card key={index} className={`hover:shadow-lg transition-shadow cursor-pointer ${module.color} border-2`}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center shadow-sm">
                            <Icon className="h-5 w-5 text-gray-700" />
                          </div>
                          <div>
                            <CardTitle className="text-lg">{module.title}</CardTitle>
                            <Badge variant="outline" className="mt-1">
                              {module.status}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 mb-4">{module.description}</p>
                      <div className="space-y-2">
                        {module.items.map((item, itemIndex) => (
                          <div key={itemIndex} className="flex items-center gap-2 text-sm">
                            <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                            {item}
                          </div>
                        ))}
                      </div>
                      <Button className="w-full mt-4" variant="outline">
                        Configure
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                System Status & Health
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">Core Services</h4>
                  {[
                    { name: "Optimization Engine", status: "healthy", uptime: "99.9%" },
                    { name: "Data Pipeline", status: "healthy", uptime: "99.7%" },
                    { name: "API Gateway", status: "healthy", uptime: "99.8%" }
                  ].map((service, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm">{service.name}</span>
                      </div>
                      <span className="text-xs text-gray-600">{service.uptime}</span>
                    </div>
                  ))}
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">Infrastructure</h4>
                  {[
                    { name: "Database Cluster", status: "healthy", load: "67%" },
                    { name: "Cache Layer", status: "healthy", load: "43%" },
                    { name: "Message Queue", status: "healthy", load: "28%" }
                  ].map((infra, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm">{infra.name}</span>
                      </div>
                      <span className="text-xs text-gray-600">{infra.load}</span>
                    </div>
                  ))}
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">Recent Activities</h4>
                  {[
                    { action: "Model deployment", time: "2 min ago", user: "admin" },
                    { action: "Config update", time: "15 min ago", user: "engineer" },
                    { action: "System backup", time: "1 hour ago", user: "system" }
                  ].map((activity, index) => (
                    <div key={index} className="p-2 bg-gray-50 rounded">
                      <div className="text-sm font-medium">{activity.action}</div>
                      <div className="text-xs text-gray-600">{activity.time} by {activity.user}</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Engineering Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Button variant="outline" className="flex items-center gap-2 h-12">
                  <TestTube className="h-4 w-4" />
                  Run Simulation
                </Button>
                <Button variant="outline" className="flex items-center gap-2 h-12">
                  <Target className="h-4 w-4" />
                  Deploy Model
                </Button>
                <Button variant="outline" className="flex items-center gap-2 h-12">
                  <Shield className="h-4 w-4" />
                  Security Audit
                </Button>
                <Button variant="outline" className="flex items-center gap-2 h-12">
                  <HardDrive className="h-4 w-4" />
                  Backup System
                </Button>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  );
};

export default Build;
