
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  ShoppingCart, 
  Package, 
  DollarSign, 
  MapPin, 
  Brain,
  TrendingUp,
  Users,
  Clock,
  Truck,
  BarChart3,
  Target,
  Zap,
  Bot,
  Settings
} from "lucide-react";
import JnaMenuTab from "@/components/commerce/JnaMenuTab";
import JnaStockTab from "@/components/commerce/JnaStockTab";
import JnaPredictTab from "@/components/commerce/JnaPredictTab";
import JnaPriceTab from "@/components/commerce/JnaPriceTab";
import JnaRouteTab from "@/components/commerce/JnaRouteTab";
import JnaOpsTab from "@/components/commerce/JnaOpsTab";
import JnaAssistantTab from "@/components/commerce/JnaAssistantTab";
import JnaAnalyticsTab from "@/components/commerce/JnaAnalyticsTab";
import ONDCGatewayTab from "@/components/commerce/ONDCGatewayTab";

const Commerce = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                <ShoppingCart className="h-6 w-6" />
                JñaOS Commerce Mode
              </h1>
              <p className="text-gray-600 mt-1">Complete business operations management platform</p>
            </div>

            <div className="flex items-center gap-3">
              <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                JñaPredict Active
              </div>
              <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border">
                Live Orders: 47
              </div>
            </div>
          </div>

          {/* Key Metrics Dashboard */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">324</div>
                    <div className="text-sm text-gray-600">Active SKUs</div>
                  </div>
                  <Package className="h-8 w-8 text-blue-500 opacity-75" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-green-600">₹2,84,700</div>
                    <div className="text-sm text-gray-600">Today's Revenue</div>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-500 opacity-75" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-purple-600">18</div>
                    <div className="text-sm text-gray-600">Active Zones</div>
                  </div>
                  <MapPin className="h-8 w-8 text-purple-500 opacity-75" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-orange-600">23 min</div>
                    <div className="text-sm text-gray-600">Avg Delivery Time</div>
                  </div>
                  <Clock className="h-8 w-8 text-orange-500 opacity-75" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* JñaOS Commerce Platform */}
          <Tabs defaultValue="dashboard" className="space-y-4">
            <TabsList className="grid grid-cols-9 w-full">
              <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
              <TabsTrigger value="catalog">Catalog</TabsTrigger>
              <TabsTrigger value="inventory">Inventory</TabsTrigger>
              <TabsTrigger value="orders">Orders</TabsTrigger>
              <TabsTrigger value="delivery">Delivery</TabsTrigger>
              <TabsTrigger value="pricing">Pricing</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="ondc">ONDC</TabsTrigger>
              <TabsTrigger value="assistant">Assistant</TabsTrigger>
            </TabsList>

            <TabsContent value="dashboard" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      Today's Performance
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Orders Processed</span>
                        <Badge variant="secondary">127 (+15%)</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Average Order Value</span>
                        <Badge variant="secondary">₹224.50 (+8%)</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Delivery Success Rate</span>
                        <Badge variant="secondary">98.5%</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Stock Availability</span>
                        <Badge variant="secondary">94.2%</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Brain className="h-5 w-5" />
                      JñaPredict Insights
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="p-3 bg-red-50 rounded-lg">
                        <div className="text-sm font-medium text-red-800">Restock Alert</div>
                        <div className="text-xs text-red-600">Milk (1L Packet) running low in Zone 3 (12 left)</div>
                      </div>
                      <div className="p-3 bg-green-50 rounded-lg">
                        <div className="text-sm font-medium text-green-800">Pricing Opportunity</div>
                        <div className="text-xs text-green-600">Increase bread price by 8% in high-demand zones</div>
                      </div>
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <div className="text-sm font-medium text-blue-800">Hot Zone Alert</div>
                        <div className="text-xs text-blue-600">Zone 7 showing 25% spike in evening orders</div>
                      </div>
                      <div className="p-3 bg-purple-50 rounded-lg">
                        <div className="text-sm font-medium text-purple-800">Route Optimization</div>
                        <div className="text-xs text-purple-600">Consolidate deliveries in Sector 15 for efficiency</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Truck className="h-5 w-5" />
                      Live Operations
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Active Deliveries</span>
                        <Badge className="bg-green-100 text-green-800">23</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Pending Dispatch</span>
                        <Badge className="bg-orange-100 text-orange-800">8</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Route Efficiency</span>
                        <Badge className="bg-blue-100 text-blue-800">94%</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">ONDC Orders</span>
                        <Badge className="bg-purple-100 text-purple-800">31</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Bot className="h-5 w-5" />
                      JñaAssistant Quick Actions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <Zap className="h-4 w-4 mr-2" />
                        Reorder milk in Zone 5
                      </Button>
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <Target className="h-4 w-4 mr-2" />
                        Optimize routes for next hour
                      </Button>
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <BarChart3 className="h-4 w-4 mr-2" />
                        Generate demand forecast
                      </Button>
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <Settings className="h-4 w-4 mr-2" />
                        Adjust pricing for peak hours
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="catalog">
              <JnaMenuTab />
            </TabsContent>

            <TabsContent value="inventory">
              <JnaStockTab />
            </TabsContent>

            <TabsContent value="orders">
              <JnaOpsTab />
            </TabsContent>

            <TabsContent value="delivery">
              <JnaRouteTab />
            </TabsContent>

            <TabsContent value="pricing">
              <JnaPriceTab />
            </TabsContent>

            <TabsContent value="analytics">
              <JnaPredictTab />
            </TabsContent>

            <TabsContent value="ondc">
              <ONDCGatewayTab />
            </TabsContent>

            <TabsContent value="assistant">
              <JnaAssistantTab />
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default Commerce;
