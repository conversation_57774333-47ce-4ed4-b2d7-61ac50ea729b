import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Play, 
  Square, 
  Download, 
  Settings, 
  Zap, 
  Target, 
  Clock, 
  CheckCircle,
  AlertCircle,
  TrendingUp,
  BarChart3,
  Cpu
} from "lucide-react";

const Optimize = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationProgress, setOptimizationProgress] = useState(0);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const startOptimization = () => {
    setIsOptimizing(true);
    setOptimizationProgress(0);
    
    // Simulate optimization progress
    const interval = setInterval(() => {
      setOptimizationProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsOptimizing(false);
          return 100;
        }
        return prev + 10;
      });
    }, 500);
  };

  const stopOptimization = () => {
    setIsOptimizing(false);
    setOptimizationProgress(0);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Supply Chain Optimization</h1>
              <p className="text-gray-600 mt-1">Run optimization algorithms to improve network efficiency and reduce costs</p>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                Gurobi Solver Ready
              </div>
              <div className="text-sm text-gray-500 bg-white px-3 py-1 rounded-full border">
                Last run: 2 hours ago
              </div>
            </div>
          </div>

          {/* Optimization Controls */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Control Panel */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Optimization Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Optimization Model</label>
                    <Select defaultValue="network-flow">
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="network-flow">Network Flow Optimization</SelectItem>
                        <SelectItem value="inventory">Inventory Optimization</SelectItem>
                        <SelectItem value="routing">Vehicle Routing Problem</SelectItem>
                        <SelectItem value="facility">Facility Location</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Objective Function</label>
                    <Select defaultValue="minimize-cost">
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="minimize-cost">Minimize Total Cost</SelectItem>
                        <SelectItem value="maximize-service">Maximize Service Level</SelectItem>
                        <SelectItem value="balance">Cost-Service Balance</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Time Horizon</label>
                    <Select defaultValue="monthly">
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="pt-4 space-y-2">
                    {!isOptimizing ? (
                      <Button onClick={startOptimization} className="w-full flex items-center gap-2">
                        <Play className="h-4 w-4" />
                        Start Optimization
                      </Button>
                    ) : (
                      <Button onClick={stopOptimization} variant="destructive" className="w-full flex items-center gap-2">
                        <Square className="h-4 w-4" />
                        Stop Optimization
                      </Button>
                    )}
                    
                    <Button variant="outline" className="w-full flex items-center gap-2">
                      <Download className="h-4 w-4" />
                      Export Results
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Optimization Status */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Cpu className="h-5 w-5" />
                    Optimization Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {isOptimizing ? (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Running Network Flow Optimization...</span>
                        <Badge variant="outline" className="bg-blue-50 text-blue-700">
                          In Progress
                        </Badge>
                      </div>
                      <Progress value={optimizationProgress} className="w-full" />
                      <div className="text-sm text-gray-600">
                        Progress: {optimizationProgress}% - Evaluating {Math.floor(optimizationProgress * 100)} solutions
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Optimize</h3>
                      <p className="text-gray-600">Configure your optimization parameters and click "Start Optimization" to begin.</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Recent Optimization Results */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Recent Optimization Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    id: 1,
                    model: "Network Flow Optimization",
                    status: "completed",
                    runtime: "2m 34s",
                    improvement: "18.5%",
                    savings: "$127K",
                    timestamp: "2 hours ago"
                  },
                  {
                    id: 2,
                    model: "Inventory Optimization",
                    status: "completed",
                    runtime: "1m 12s",
                    improvement: "12.3%",
                    savings: "$89K",
                    timestamp: "1 day ago"
                  },
                  {
                    id: 3,
                    model: "Vehicle Routing Problem",
                    status: "failed",
                    runtime: "5m 45s",
                    improvement: "-",
                    savings: "-",
                    timestamp: "2 days ago"
                  }
                ].map((result) => (
                  <div key={result.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      {result.status === "completed" ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-red-500" />
                      )}
                      <div>
                        <div className="font-medium">{result.model}</div>
                        <div className="text-sm text-gray-600">Runtime: {result.runtime}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-4">
                        <div>
                          <div className="text-sm font-medium">{result.improvement} improvement</div>
                          <div className="text-sm text-gray-600">{result.savings} savings</div>
                        </div>
                        <div className="text-sm text-gray-500">{result.timestamp}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Optimization Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-blue-600">47</div>
              <div className="text-sm text-gray-600">Total Optimizations</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-green-600">94.7%</div>
              <div className="text-sm text-gray-600">Success Rate</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-purple-600">$2.3M</div>
              <div className="text-sm text-gray-600">Total Savings</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="text-2xl font-bold text-orange-600">3m 42s</div>
              <div className="text-sm text-gray-600">Avg Runtime</div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Optimize;
