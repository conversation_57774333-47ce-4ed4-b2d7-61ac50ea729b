
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Warehouse, Users, TrendingUp, Package, Plus, Download, Upload, Search, Filter, Settings } from "lucide-react";
import WarehouseMetrics from "@/components/warehouses/WarehouseMetrics";
import WarehouseChart from "@/components/warehouses/WarehouseChart";
import { mockWarehouses, mockWarehousePerformance, calculateWarehouseMetrics, getStatusColor, getAutomationColor } from "@/utils/warehouseUtils";

const Warehouses = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const metrics = calculateWarehouseMetrics(mockWarehouses);

  const utilizationData = mockWarehouses.map(warehouse => ({
    name: warehouse.name.split(' ')[0],
    utilization: warehouse.utilization,
    capacity: warehouse.capacity
  }));

  const performanceData = mockWarehousePerformance.map(perf => {
    const warehouse = mockWarehouses.find(w => w.id === perf.warehouseId);
    return {
      name: warehouse?.name.split(' ')[0] || perf.warehouseId,
      efficiency: perf.efficiency,
      accuracy: perf.accuracy,
      throughput: perf.throughput
    };
  });

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Warehouses Management</h1>
              <p className="text-gray-600 mt-1">Monitor and manage distribution facilities</p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Warehouse
              </Button>
            </div>
          </div>

          {/* Summary Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <WarehouseMetrics
              icon={Warehouse}
              iconColor="text-blue-500"
              label="Total Warehouses"
              value={metrics.totalWarehouses}
              change={{ value: 8, isPositive: true }}
            />
            <WarehouseMetrics
              icon={Package}
              iconColor="text-green-500"
              label="Active Warehouses"
              value={metrics.activeWarehouses}
              change={{ value: 3, isPositive: true }}
            />
            <WarehouseMetrics
              icon={TrendingUp}
              iconColor="text-purple-500"
              label="Avg Utilization"
              value={`${Math.round(metrics.averageUtilization)}%`}
              change={{ value: 5, isPositive: true }}
            />
            <WarehouseMetrics
              icon={Users}
              iconColor="text-orange-500"
              label="Total Employees"
              value={metrics.totalEmployees.toLocaleString()}
              change={{ value: 12, isPositive: true }}
            />
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <WarehouseChart
              title="Warehouse Utilization"
              description="Current capacity utilization across warehouses"
              data={utilizationData}
              type="bar"
              dataKey="utilization"
              xAxisKey="name"
              color="#3b82f6"
            />
            <WarehouseChart
              title="Performance Metrics"
              description="Efficiency trends across facilities"
              data={performanceData}
              type="line"
              dataKey="efficiency"
              xAxisKey="name"
              color="#10b981"
            />
          </div>

          {/* Main Content */}
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="operations">Operations</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Warehouse Overview</CardTitle>
                      <CardDescription>Complete list of distribution facilities</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Search className="h-4 w-4 mr-2" />
                        Search
                      </Button>
                      <Button variant="outline" size="sm">
                        <Filter className="h-4 w-4 mr-2" />
                        Filter
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-3">Warehouse ID</th>
                          <th className="text-left p-3">Name</th>
                          <th className="text-left p-3">Location</th>
                          <th className="text-left p-3">Type</th>
                          <th className="text-left p-3">Status</th>
                          <th className="text-right p-3">Utilization</th>
                          <th className="text-right p-3">Employees</th>
                          <th className="text-left p-3">Automation</th>
                          <th className="text-left p-3">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {mockWarehouses.map((warehouse) => (
                          <tr key={warehouse.id} className="border-b hover:bg-gray-50">
                            <td className="p-3 font-mono text-sm">{warehouse.id}</td>
                            <td className="p-3 font-medium">{warehouse.name}</td>
                            <td className="p-3">{warehouse.location}</td>
                            <td className="p-3">{warehouse.type}</td>
                            <td className="p-3">
                              <Badge className={getStatusColor(warehouse.status)}>
                                {warehouse.status}
                              </Badge>
                            </td>
                            <td className="p-3 text-right">{warehouse.utilization}%</td>
                            <td className="p-3 text-right">{warehouse.employees}</td>
                            <td className="p-3">
                              <Badge className={getAutomationColor(warehouse.automationLevel)}>
                                {warehouse.automationLevel}
                              </Badge>
                            </td>
                            <td className="p-3">
                              <Button variant="outline" size="sm">
                                <Settings className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Analytics</CardTitle>
                  <CardDescription>Key performance indicators for all warehouses</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-3">Warehouse</th>
                          <th className="text-right p-3">Efficiency</th>
                          <th className="text-right p-3">Accuracy</th>
                          <th className="text-right p-3">Throughput</th>
                          <th className="text-right p-3">Order Fulfillment</th>
                          <th className="text-right p-3">Picking Speed</th>
                          <th className="text-right p-3">Error Rate</th>
                        </tr>
                      </thead>
                      <tbody>
                        {mockWarehousePerformance.map((perf) => {
                          const warehouse = mockWarehouses.find(w => w.id === perf.warehouseId);
                          return (
                            <tr key={perf.warehouseId} className="border-b hover:bg-gray-50">
                              <td className="p-3 font-medium">{warehouse?.name}</td>
                              <td className="p-3 text-right">{perf.efficiency}%</td>
                              <td className="p-3 text-right">{perf.accuracy}%</td>
                              <td className="p-3 text-right">{perf.throughput}/day</td>
                              <td className="p-3 text-right">{perf.orderFulfillment}%</td>
                              <td className="p-3 text-right">{perf.pickingSpeed}/hr</td>
                              <td className="p-3 text-right">{perf.errorRate}%</td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="operations" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Operations Status</CardTitle>
                  <CardDescription>Current operational status and activities</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockWarehouses.map((warehouse) => (
                      <div key={warehouse.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">{warehouse.name}</p>
                          <p className="text-sm text-gray-600">{warehouse.location}</p>
                          <p className="text-sm text-gray-500">Zones: {warehouse.zones.join(", ")}</p>
                        </div>
                        <div className="text-right">
                          <Badge className={getStatusColor(warehouse.status)}>
                            {warehouse.status}
                          </Badge>
                          <p className="text-sm text-gray-600 mt-1">
                            {warehouse.temperatureControlled ? "Climate Controlled" : "Standard"}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Automation Levels</CardTitle>
                    <CardDescription>Distribution of automation across facilities</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {mockWarehouses.map((warehouse) => (
                        <div key={warehouse.id} className="flex items-center justify-between">
                          <span className="text-sm">{warehouse.name}</span>
                          <Badge className={getAutomationColor(warehouse.automationLevel)}>
                            {warehouse.automationLevel}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Capacity Analysis</CardTitle>
                    <CardDescription>Capacity vs utilization breakdown</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {mockWarehouses.map((warehouse) => (
                        <div key={warehouse.id} className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span>{warehouse.name}</span>
                            <span>{warehouse.utilization}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-500 h-2 rounded-full" 
                              style={{ width: `${warehouse.utilization}%` }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default Warehouses;
