
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Settings, Save, RefreshCw, AlertCircle, CheckCircle, Thermometer } from "lucide-react";

const WarehouseConfig = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Warehouse Configuration</h1>
              <p className="text-gray-600 mt-1">Configure warehouse settings and operations</p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button size="sm">
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </div>
          </div>

          {/* Configuration Tabs */}
          <Tabs defaultValue="general" className="space-y-4">
            <TabsList>
              <TabsTrigger value="general">General Settings</TabsTrigger>
              <TabsTrigger value="storage">Storage & Layout</TabsTrigger>
              <TabsTrigger value="automation">Automation</TabsTrigger>
              <TabsTrigger value="operations">Operations</TabsTrigger>
              <TabsTrigger value="environment">Environment</TabsTrigger>
            </TabsList>
            
            <TabsContent value="general" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>General Warehouse Settings</CardTitle>
                  <CardDescription>Basic configuration for warehouse operations</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="warehouse-name">Warehouse Name</Label>
                      <Input id="warehouse-name" placeholder="Enter warehouse name" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="warehouse-code">Warehouse Code</Label>
                      <Input id="warehouse-code" placeholder="e.g., WH001" />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="warehouse-type">Warehouse Type</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select warehouse type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="distribution">Distribution</SelectItem>
                          <SelectItem value="storage">Storage</SelectItem>
                          <SelectItem value="cross-dock">Cross-Dock</SelectItem>
                          <SelectItem value="fulfillment">Fulfillment</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="warehouse-region">Region</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select region" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="southeast">Southeast</SelectItem>
                          <SelectItem value="west-coast">West Coast</SelectItem>
                          <SelectItem value="midwest">Midwest</SelectItem>
                          <SelectItem value="northeast">Northeast</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="warehouse-description">Description</Label>
                    <Textarea id="warehouse-description" placeholder="Warehouse description and notes" />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="active-status" />
                    <Label htmlFor="active-status">Warehouse is active</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="storage" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Storage & Layout Configuration</CardTitle>
                  <CardDescription>Configure storage capacity and warehouse layout</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="total-area">Total Area (sq ft)</Label>
                      <Input id="total-area" type="number" placeholder="e.g., 500000" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="storage-capacity">Storage Capacity</Label>
                      <Input id="storage-capacity" type="number" placeholder="Total units" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="ceiling-height">Ceiling Height (ft)</Label>
                      <Input id="ceiling-height" type="number" placeholder="e.g., 30" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="storage-zones">Storage Zones</Label>
                    <Input id="storage-zones" placeholder="e.g., Receiving, Storage, Picking, Shipping" />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="rack-configuration">Rack Configuration</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select rack type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="selective">Selective Racking</SelectItem>
                          <SelectItem value="drive-in">Drive-in Racking</SelectItem>
                          <SelectItem value="push-back">Push-back Racking</SelectItem>
                          <SelectItem value="pallet-flow">Pallet Flow</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="storage-method">Storage Method</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select storage method" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="fifo">FIFO (First In, First Out)</SelectItem>
                          <SelectItem value="lifo">LIFO (Last In, First Out)</SelectItem>
                          <SelectItem value="fefo">FEFO (First Expired, First Out)</SelectItem>
                          <SelectItem value="random">Random Storage</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="multi-level" />
                    <Label htmlFor="multi-level">Multi-level storage system</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="automation" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Automation Settings</CardTitle>
                  <CardDescription>Configure warehouse automation systems</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="automation-level">Automation Level</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select automation level" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="manual">Manual</SelectItem>
                          <SelectItem value="semi-automated">Semi-Automated</SelectItem>
                          <SelectItem value="fully-automated">Fully Automated</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="wms-system">WMS System</Label>
                      <Input id="wms-system" placeholder="e.g., SAP WM, Manhattan WMS" />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="automated-picking">Automated Picking</Label>
                        <p className="text-sm text-gray-600">Enable automated picking systems</p>
                      </div>
                      <Switch id="automated-picking" />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="conveyor-systems">Conveyor Systems</Label>
                        <p className="text-sm text-gray-600">Automated conveyor belts</p>
                      </div>
                      <Switch id="conveyor-systems" />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="agv-systems">AGV Systems</Label>
                        <p className="text-sm text-gray-600">Automated Guided Vehicles</p>
                      </div>
                      <Switch id="agv-systems" />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="sorting-systems">Automated Sorting</Label>
                        <p className="text-sm text-gray-600">Automated sorting systems</p>
                      </div>
                      <Switch id="sorting-systems" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="operations" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Operations Configuration</CardTitle>
                  <CardDescription>Configure operational parameters and workflows</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="operating-hours">Operating Hours</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select hours" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="8">8 Hours</SelectItem>
                          <SelectItem value="16">16 Hours</SelectItem>
                          <SelectItem value="24">24 Hours</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="shifts-per-day">Shifts per Day</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select shifts" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 Shift</SelectItem>
                          <SelectItem value="2">2 Shifts</SelectItem>
                          <SelectItem value="3">3 Shifts</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="max-employees">Max Employees</Label>
                      <Input id="max-employees" type="number" placeholder="e.g., 200" />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="picking-strategy">Picking Strategy</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select strategy" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="discrete">Discrete Picking</SelectItem>
                          <SelectItem value="batch">Batch Picking</SelectItem>
                          <SelectItem value="zone">Zone Picking</SelectItem>
                          <SelectItem value="wave">Wave Picking</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cycle-count-frequency">Cycle Count Frequency</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="quarterly">Quarterly</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="cross-docking">Cross-Docking</Label>
                        <p className="text-sm text-gray-600">Enable cross-docking operations</p>
                      </div>
                      <Switch id="cross-docking" />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="kitting-services">Kitting Services</Label>
                        <p className="text-sm text-gray-600">Assembly and kitting services</p>
                      </div>
                      <Switch id="kitting-services" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="environment" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Environment Configuration</CardTitle>
                  <CardDescription>Environmental controls and safety settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="min-temperature">Min Temperature (°F)</Label>
                      <Input id="min-temperature" type="number" placeholder="e.g., 32" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="max-temperature">Max Temperature (°F)</Label>
                      <Input id="max-temperature" type="number" placeholder="e.g., 75" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="humidity-control">Humidity Range (%)</Label>
                      <Input id="humidity-control" placeholder="e.g., 40-60%" />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="temperature-controlled">Temperature Controlled</Label>
                        <p className="text-sm text-gray-600">Climate-controlled environment</p>
                      </div>
                      <Switch id="temperature-controlled" />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="cold-storage">Cold Storage</Label>
                        <p className="text-sm text-gray-600">Refrigerated storage areas</p>
                      </div>
                      <Switch id="cold-storage" />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="hazmat-storage">Hazmat Storage</Label>
                        <p className="text-sm text-gray-600">Hazardous materials storage</p>
                      </div>
                      <Switch id="hazmat-storage" />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="fire-suppression">Fire Suppression</Label>
                        <p className="text-sm text-gray-600">Advanced fire suppression system</p>
                      </div>
                      <Switch id="fire-suppression" />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="security-system">Security System</Label>
                        <p className="text-sm text-gray-600">24/7 security monitoring</p>
                      </div>
                      <Switch id="security-system" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="environmental-certifications">Environmental Certifications</Label>
                    <Input id="environmental-certifications" placeholder="e.g., LEED, ISO 14001" />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default WarehouseConfig;
