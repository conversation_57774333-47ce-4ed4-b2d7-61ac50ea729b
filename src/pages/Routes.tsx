
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Search, Plus, Route, Clock, DollarSign, TrendingUp, MapPin, Truck } from "lucide-react";

const Routes = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const routes = [
    {
      id: "RT-001",
      name: "Atlanta - Savannah Express",
      origin: "Atlanta, GA",
      destination: "Savannah, GA",
      distance: "248 mi",
      estimatedTime: "4h 15m",
      cost: "$389",
      status: "Active",
      carrier: "Federal Express",
      priority: "High",
      frequency: "Daily",
      capacity: "85%",
      lastOptimized: "2 days ago"
    },
    {
      id: "RT-002",
      name: "Charlotte - Augusta Regional",
      origin: "Charlotte, NC",
      destination: "Augusta, GA", 
      distance: "156 mi",
      estimatedTime: "2h 45m",
      cost: "$245",
      status: "Active",
      carrier: "UPS Logistics",
      priority: "Medium",
      frequency: "3x/week",
      capacity: "67%",
      lastOptimized: "1 week ago"
    },
    {
      id: "RT-003",
      name: "Miami - Jacksonville Highway",
      origin: "Miami, FL",
      destination: "Jacksonville, FL",
      distance: "347 mi",
      estimatedTime: "5h 30m",
      cost: "$456",
      status: "Optimization Needed",
      carrier: "Regional Express Co",
      priority: "Medium",
      frequency: "2x/week",
      capacity: "92%",
      lastOptimized: "3 weeks ago"
    },
    {
      id: "RT-004",
      name: "Tampa - Orlando Direct",
      origin: "Tampa, FL",
      destination: "Orlando, FL",
      distance: "85 mi",
      estimatedTime: "1h 30m",
      cost: "$134",
      status: "Active",
      carrier: "USPS Priority",
      priority: "Low",
      frequency: "Daily",
      capacity: "45%",
      lastOptimized: "5 days ago"
    },
    {
      id: "RT-005",
      name: "Birmingham - Montgomery Loop",
      origin: "Birmingham, AL",
      destination: "Montgomery, AL",
      distance: "93 mi",
      estimatedTime: "1h 45m",
      cost: "$156",
      status: "Under Review",
      carrier: "DHL Supply Chain",
      priority: "High",
      frequency: "5x/week",
      capacity: "78%",
      lastOptimized: "1 day ago"
    }
  ];

  const filteredRoutes = routes.filter(route =>
    route.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    route.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    route.origin.toLowerCase().includes(searchTerm.toLowerCase()) ||
    route.destination.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Under Review":
        return "bg-yellow-100 text-yellow-800";
      case "Optimization Needed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High":
        return "bg-red-100 text-red-800";
      case "Medium":
        return "bg-yellow-100 text-yellow-800";
      case "Low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getCapacityColor = (capacity: string) => {
    const percent = parseInt(capacity);
    if (percent >= 90) return "text-red-600";
    if (percent >= 70) return "text-yellow-600";
    return "text-green-600";
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Routes</h1>
              <p className="text-gray-600 mt-1">Manage and optimize your transportation routes</p>
            </div>
            
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Route
            </Button>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-900">247</div>
                    <div className="text-sm text-gray-600">Total Routes</div>
                  </div>
                  <Route className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-900">3.2h</div>
                    <div className="text-sm text-gray-600">Avg Transit Time</div>
                  </div>
                  <Clock className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-900">$276</div>
                    <div className="text-sm text-gray-600">Avg Route Cost</div>
                  </div>
                  <DollarSign className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-900">15</div>
                    <div className="text-sm text-gray-600">Need Optimization</div>
                  </div>
                  <TrendingUp className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Search and Filters */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search routes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">Filter</Button>
            <Button variant="outline">Optimize All</Button>
          </div>

          {/* Routes Table */}
          <Card>
            <CardHeader>
              <CardTitle>Route Directory</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredRoutes.map((route) => (
                  <div key={route.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="bg-blue-100 p-2 rounded-lg">
                          <Route className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">{route.name}</h3>
                          <div className="text-sm text-gray-600 flex items-center gap-2">
                            <span className="font-medium">{route.id}</span>
                            <span>•</span>
                            <span>{route.carrier}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(route.status)}>{route.status}</Badge>
                        <Badge className={getPriorityColor(route.priority)}>{route.priority}</Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-4">
                      <div>
                        <div className="text-xs text-gray-500 mb-1">ORIGIN</div>
                        <div className="text-sm font-medium flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-green-600" />
                          {route.origin}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500 mb-1">DESTINATION</div>
                        <div className="text-sm font-medium flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-red-600" />
                          {route.destination}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500 mb-1">DISTANCE</div>
                        <div className="text-sm font-medium">{route.distance}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500 mb-1">TIME</div>
                        <div className="text-sm font-medium flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {route.estimatedTime}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500 mb-1">COST</div>
                        <div className="text-sm font-medium text-green-600">{route.cost}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500 mb-1">CAPACITY</div>
                        <div className={`text-sm font-medium ${getCapacityColor(route.capacity)}`}>
                          {route.capacity}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span className="flex items-center gap-1">
                          <Truck className="h-3 w-3" />
                          {route.frequency}
                        </span>
                        <span>Last optimized: {route.lastOptimized}</span>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">View Details</Button>
                        <Button variant="outline" size="sm">Optimize</Button>
                        <Button variant="outline" size="sm">Edit</Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  );
};

export default Routes;
