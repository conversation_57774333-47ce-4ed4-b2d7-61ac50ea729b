
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Settings, Save, RefreshCw, AlertCircle, CheckCircle } from "lucide-react";

const PlantConfig = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Plant Configuration</h1>
              <p className="text-gray-600 mt-1">Configure plant settings and parameters</p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button size="sm">
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </div>
          </div>

          {/* Configuration Tabs */}
          <Tabs defaultValue="general" className="space-y-4">
            <TabsList>
              <TabsTrigger value="general">General Settings</TabsTrigger>
              <TabsTrigger value="capacity">Capacity Management</TabsTrigger>
              <TabsTrigger value="automation">Automation</TabsTrigger>
              <TabsTrigger value="quality">Quality Control</TabsTrigger>
              <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
            </TabsList>
            
            <TabsContent value="general" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>General Plant Settings</CardTitle>
                  <CardDescription>Basic configuration for plant operations</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="plant-name">Plant Name</Label>
                      <Input id="plant-name" placeholder="Enter plant name" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="plant-code">Plant Code</Label>
                      <Input id="plant-code" placeholder="e.g., PLT001" />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="plant-type">Plant Type</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select plant type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="manufacturing">Manufacturing</SelectItem>
                          <SelectItem value="assembly">Assembly</SelectItem>
                          <SelectItem value="processing">Processing</SelectItem>
                          <SelectItem value="packaging">Packaging</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="plant-region">Region</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select region" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="north">North</SelectItem>
                          <SelectItem value="south">South</SelectItem>
                          <SelectItem value="east">East</SelectItem>
                          <SelectItem value="west">West</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="plant-description">Description</Label>
                    <Textarea id="plant-description" placeholder="Plant description and notes" />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="active-status" />
                    <Label htmlFor="active-status">Plant is active</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="capacity" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Capacity Management</CardTitle>
                  <CardDescription>Configure production capacity and limits</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="max-capacity">Maximum Capacity</Label>
                      <Input id="max-capacity" type="number" placeholder="Units per day" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="current-capacity">Current Capacity</Label>
                      <Input id="current-capacity" type="number" placeholder="Units per day" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="target-utilization">Target Utilization (%)</Label>
                      <Input id="target-utilization" type="number" placeholder="e.g., 85" />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="shift-pattern">Shift Pattern</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select shift pattern" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="single">Single Shift (8 hours)</SelectItem>
                          <SelectItem value="double">Double Shift (16 hours)</SelectItem>
                          <SelectItem value="triple">Triple Shift (24 hours)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="working-days">Working Days per Week</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select working days" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="5">5 Days</SelectItem>
                          <SelectItem value="6">6 Days</SelectItem>
                          <SelectItem value="7">7 Days</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="flexible-capacity" />
                    <Label htmlFor="flexible-capacity">Enable flexible capacity scaling</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="automation" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Automation Settings</CardTitle>
                  <CardDescription>Configure automation levels and systems</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="automation-level">Automation Level</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select automation level" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="manual">Manual</SelectItem>
                          <SelectItem value="semi-automated">Semi-Automated</SelectItem>
                          <SelectItem value="fully-automated">Fully Automated</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="automation-systems">Primary Systems</Label>
                      <Input id="automation-systems" placeholder="e.g., PLC, SCADA, MES" />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="robotic-systems">Robotic Systems</Label>
                        <p className="text-sm text-gray-600">Enable robotic automation</p>
                      </div>
                      <Switch id="robotic-systems" />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="ai-quality">AI Quality Control</Label>
                        <p className="text-sm text-gray-600">AI-powered quality inspection</p>
                      </div>
                      <Switch id="ai-quality" />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="predictive-maintenance">Predictive Maintenance</Label>
                        <p className="text-sm text-gray-600">AI-driven maintenance scheduling</p>
                      </div>
                      <Switch id="predictive-maintenance" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="quality" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Quality Control Configuration</CardTitle>
                  <CardDescription>Set quality standards and monitoring parameters</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="quality-target">Quality Target (%)</Label>
                      <Input id="quality-target" type="number" placeholder="e.g., 99.5" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="defect-threshold">Defect Threshold (%)</Label>
                      <Input id="defect-threshold" type="number" placeholder="e.g., 0.5" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="inspection-frequency">Inspection Frequency</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="continuous">Continuous</SelectItem>
                          <SelectItem value="hourly">Hourly</SelectItem>
                          <SelectItem value="shift">Per Shift</SelectItem>
                          <SelectItem value="daily">Daily</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auto-reject">Automatic Rejection</Label>
                        <p className="text-sm text-gray-600">Auto-reject defective products</p>
                      </div>
                      <Switch id="auto-reject" />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="quality-alerts">Quality Alerts</Label>
                        <p className="text-sm text-gray-600">Real-time quality notifications</p>
                      </div>
                      <Switch id="quality-alerts" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="maintenance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Maintenance Configuration</CardTitle>
                  <CardDescription>Setup maintenance schedules and procedures</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="maintenance-strategy">Maintenance Strategy</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select strategy" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="preventive">Preventive</SelectItem>
                          <SelectItem value="predictive">Predictive</SelectItem>
                          <SelectItem value="reactive">Reactive</SelectItem>
                          <SelectItem value="hybrid">Hybrid</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maintenance-frequency">Frequency</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="quarterly">Quarterly</SelectItem>
                          <SelectItem value="annual">Annual</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="downtime-target">Target Downtime (%)</Label>
                      <Input id="downtime-target" type="number" placeholder="e.g., 2.0" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="mtbf-target">MTBF Target (hours)</Label>
                      <Input id="mtbf-target" type="number" placeholder="e.g., 1000" />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auto-scheduling">Automatic Scheduling</Label>
                        <p className="text-sm text-gray-600">Auto-schedule maintenance tasks</p>
                      </div>
                      <Switch id="auto-scheduling" />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="emergency-protocols">Emergency Protocols</Label>
                        <p className="text-sm text-gray-600">Enable emergency maintenance procedures</p>
                      </div>
                      <Switch id="emergency-protocols" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default PlantConfig;
