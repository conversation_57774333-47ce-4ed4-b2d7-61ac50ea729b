
import { useState } from "react";
import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import ConfirmationDialog from "@/components/ConfirmationDialog";
import { Package, MapPin, Plus, Download, Upload, Search, Edit, Trash2, Phone, Mail, Globe } from "lucide-react";

const Suppliers = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState<number | null>(null);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const suppliers = [
    { 
      id: 1, 
      name: "Acme Manufacturing Corp", 
      category: "Raw Materials", 
      location: "Chicago, IL", 
      status: "Active",
      email: "<EMAIL>",
      phone: "+****************",
      website: "www.acme-mfg.com",
      rating: 4.8,
      lastOrder: "2024-05-15"
    },
    { 
      id: 2, 
      name: "Global Parts Supplier", 
      category: "Components", 
      location: "Detroit, MI", 
      status: "Active",
      email: "<EMAIL>",
      phone: "+****************",
      website: "www.globalparts.com",
      rating: 4.6,
      lastOrder: "2024-05-20"
    },
    { 
      id: 3, 
      name: "European Steel Co", 
      category: "Raw Materials", 
      location: "Hamburg, Germany", 
      status: "Pending",
      email: "<EMAIL>",
      phone: "+49 40 123456",
      website: "www.eurosteel.de",
      rating: 4.9,
      lastOrder: "2024-04-30"
    },
    { 
      id: 4, 
      name: "Pacific Electronics", 
      category: "Electronics", 
      location: "San Jose, CA", 
      status: "Inactive",
      email: "<EMAIL>",
      phone: "+****************",
      website: "www.pacificelectronics.com",
      rating: 4.2,
      lastOrder: "2024-03-10"
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "bg-green-100 text-green-800";
      case "Pending": return "bg-yellow-100 text-yellow-800";
      case "Inactive": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const filteredSuppliers = suppliers.filter(supplier =>
    supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDeleteSupplier = (supplierId: number) => {
    setSelectedSupplier(supplierId);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    console.log(`Deleting supplier with ID: ${selectedSupplier}`);
    // Here you would implement the actual delete logic
    setDeleteDialogOpen(false);
    setSelectedSupplier(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      <Sidebar isCollapsed={sidebarCollapsed} />
      
      <div className="flex-1 flex flex-col min-w-0">
        <Header onMenuToggle={toggleSidebar} />
        
        <main className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Supplier Management</h1>
              <p className="text-gray-600 mt-1">Manage your supplier network and partnerships</p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Supplier
              </Button>
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Package className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-600">Total Suppliers</p>
                    <p className="text-2xl font-bold">{suppliers.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Package className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-600">Active Suppliers</p>
                    <p className="text-2xl font-bold">{suppliers.filter(s => s.status === "Active").length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="text-sm text-gray-600">Locations</p>
                    <p className="text-2xl font-bold">{new Set(suppliers.map(s => s.location.split(',')[1]?.trim())).size}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Package className="h-5 w-5 text-orange-500" />
                  <div>
                    <p className="text-sm text-gray-600">Categories</p>
                    <p className="text-2xl font-bold">{new Set(suppliers.map(s => s.category)).size}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <Tabs defaultValue="suppliers" className="space-y-4">
            <TabsList>
              <TabsTrigger value="suppliers">Supplier Directory</TabsTrigger>
              <TabsTrigger value="categories">Categories</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
            </TabsList>
            
            <TabsContent value="suppliers" className="space-y-4">
              {/* Search and Filter */}
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-4">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="Search suppliers by name, category, or location..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    <Button variant="outline">
                      Filter
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Suppliers Table */}
              <Card>
                <CardHeader>
                  <CardTitle>Suppliers ({filteredSuppliers.length})</CardTitle>
                  <CardDescription>
                    Manage your supplier network and contact information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Supplier</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Contact</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Rating</TableHead>
                        <TableHead>Last Order</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredSuppliers.map((supplier) => (
                        <TableRow key={supplier.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{supplier.name}</div>
                              <div className="text-sm text-gray-500 flex items-center gap-1">
                                <Globe className="h-3 w-3" />
                                {supplier.website}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{supplier.category}</TableCell>
                          <TableCell className="flex items-center gap-1">
                            <MapPin className="h-3 w-3 text-gray-400" />
                            {supplier.location}
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="text-sm flex items-center gap-1">
                                <Mail className="h-3 w-3 text-gray-400" />
                                {supplier.email}
                              </div>
                              <div className="text-sm flex items-center gap-1">
                                <Phone className="h-3 w-3 text-gray-400" />
                                {supplier.phone}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(supplier.status)}>
                              {supplier.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <span className="text-yellow-500">★</span>
                              {supplier.rating}
                            </div>
                          </TableCell>
                          <TableCell>{supplier.lastOrder}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              <Button variant="outline" size="sm">
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleDeleteSupplier(supplier.id)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="categories" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Supplier Categories</CardTitle>
                  <CardDescription>
                    Organize suppliers by product categories and services
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {Array.from(new Set(suppliers.map(s => s.category))).map((category) => (
                      <Card key={category}>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="font-medium">{category}</h3>
                              <p className="text-sm text-gray-500">
                                {suppliers.filter(s => s.category === category).length} suppliers
                              </p>
                            </div>
                            <Package className="h-6 w-6 text-gray-400" />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Supplier Performance</CardTitle>
                  <CardDescription>
                    Track supplier performance metrics and ratings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-96 flex items-center justify-center bg-gray-50 rounded-lg">
                    <p className="text-gray-500">Performance analytics would be implemented here</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>

      <ConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Supplier"
        description="Are you sure you want to delete this supplier? This action cannot be undone."
        confirmLabel="Delete"
        variant="destructive"
        onConfirm={confirmDelete}
      />
    </div>
  );
};

export default Suppliers;
