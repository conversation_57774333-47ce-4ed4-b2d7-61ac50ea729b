
import { Plant, PlantPerformance, PlantMetrics } from "@/types/plant";

export const mockPlants: Plant[] = [
  {
    id: "PLT001",
    name: "Detroit Manufacturing",
    location: "Detroit, MI",
    type: "Manufacturing",
    status: "Active",
    capacity: 10000,
    utilization: 85,
    employees: 450,
    products: ["Auto Parts", "Electronics"],
    region: "North America",
    manager: "<PERSON>",
    establishedDate: "2010-03-15",
    area: 250000,
    certifications: ["ISO 9001", "ISO 14001"],
    energyRating: "A"
  },
  {
    id: "PLT002",
    name: "Chicago Assembly",
    location: "Chicago, IL",
    type: "Assembly",
    status: "Active",
    capacity: 8000,
    utilization: 92,
    employees: 320,
    products: ["Consumer Goods", "Industrial"],
    region: "North America",
    manager: "<PERSON>",
    establishedDate: "2015-07-22",
    area: 180000,
    certifications: ["ISO 9001", "OHSAS 18001"],
    energyRating: "B"
  },
  {
    id: "PLT003",
    name: "Houston Processing",
    location: "Houston, TX",
    type: "Processing",
    status: "Maintenance",
    capacity: 12000,
    utilization: 0,
    employees: 280,
    products: ["Chemicals", "Petroleum"],
    region: "North America",
    manager: "<PERSON>",
    establishedDate: "2008-11-10",
    area: 320000,
    certifications: ["ISO 9001", "ISO 14001", "OHSAS 18001"],
    energyRating: "B"
  },
  {
    id: "PLT004",
    name: "Phoenix Distribution",
    location: "Phoenix, AZ",
    type: "Distribution",
    status: "Active",
    capacity: 15000,
    utilization: 78,
    employees: 180,
    products: ["Consumer Goods", "Electronics"],
    region: "North America",
    manager: "Lisa Davis",
    establishedDate: "2020-01-05",
    area: 400000,
    certifications: ["ISO 9001"],
    energyRating: "A"
  }
];

export const mockPlantPerformance: PlantPerformance[] = [
  {
    plantId: "PLT001",
    efficiency: 87,
    qualityScore: 94,
    safetyScore: 96,
    onTimeDelivery: 92,
    costPerUnit: 12.5,
    maintenanceHours: 45,
    downtime: 2.1
  },
  {
    plantId: "PLT002",
    efficiency: 91,
    qualityScore: 89,
    safetyScore: 98,
    onTimeDelivery: 95,
    costPerUnit: 10.8,
    maintenanceHours: 32,
    downtime: 1.5
  },
  {
    plantId: "PLT003",
    efficiency: 0,
    qualityScore: 0,
    safetyScore: 85,
    onTimeDelivery: 0,
    costPerUnit: 0,
    maintenanceHours: 120,
    downtime: 100
  },
  {
    plantId: "PLT004",
    efficiency: 82,
    qualityScore: 91,
    safetyScore: 94,
    onTimeDelivery: 88,
    costPerUnit: 8.9,
    maintenanceHours: 28,
    downtime: 3.2
  }
];

export const calculatePlantMetrics = (plants: Plant[]): PlantMetrics => {
  const totalPlants = plants.length;
  const activePlants = plants.filter(p => p.status === "Active").length;
  const totalCapacity = plants.reduce((sum, plant) => sum + plant.capacity, 0);
  const averageUtilization = plants.reduce((sum, plant) => sum + plant.utilization, 0) / plants.length;
  const totalEmployees = plants.reduce((sum, plant) => sum + plant.employees, 0);
  const maintenancePlants = plants.filter(p => p.status === "Maintenance").length;

  return {
    totalPlants,
    activeBlants: activePlants,
    totalCapacity,
    averageUtilization,
    totalEmployees,
    maintenancePlants
  };
};

export const getStatusColor = (status: string) => {
  switch (status) {
    case "Active": return "bg-green-100 text-green-800";
    case "Inactive": return "bg-gray-100 text-gray-800";
    case "Maintenance": return "bg-yellow-100 text-yellow-800";
    case "Planned": return "bg-blue-100 text-blue-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

export const getEnergyRatingColor = (rating: string) => {
  switch (rating) {
    case "A": return "bg-green-100 text-green-800";
    case "B": return "bg-blue-100 text-blue-800";
    case "C": return "bg-yellow-100 text-yellow-800";
    case "D": return "bg-red-100 text-red-800";
    default: return "bg-gray-100 text-gray-800";
  }
};
