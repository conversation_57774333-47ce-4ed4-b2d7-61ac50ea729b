
export const getSeverityColor = (severity: string) => {
  switch (severity) {
    case "Critical": return "bg-red-100 text-red-800";
    case "High": return "bg-orange-100 text-orange-800";
    case "Medium": return "bg-yellow-100 text-yellow-800";
    case "Low": return "bg-blue-100 text-blue-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

export const getStatusColor = (status: string) => {
  switch (status) {
    case "Active": return "bg-green-100 text-green-800";
    case "Inactive": return "bg-gray-100 text-gray-800";
    case "Resolved": return "bg-green-100 text-green-800";
    case "Under Review": return "bg-yellow-100 text-yellow-800";
    case "Pending Investigation": return "bg-red-100 text-red-800";
    default: return "bg-gray-100 text-gray-800";
  }
};
