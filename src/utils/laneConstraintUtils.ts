
export const getOverrideTypeColor = (type: string) => {
  switch (type) {
    case "Value Override": return "bg-blue-100 text-blue-800";
    case "Condition Override": return "bg-purple-100 text-purple-800";
    case "Time Override": return "bg-green-100 text-green-800";
    case "Emergency Override": return "bg-red-100 text-red-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

export const getTemplateTypeColor = (category: string) => {
  switch (category) {
    case "Interstate": return "bg-blue-100 text-blue-800";
    case "Urban": return "bg-green-100 text-green-800";
    case "Hazmat": return "bg-red-100 text-red-800";
    case "Express": return "bg-purple-100 text-purple-800";
    case "Regional": return "bg-yellow-100 text-yellow-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

export const getPriorityColor = (priority: number) => {
  if (priority >= 9) return "bg-red-100 text-red-800";
  if (priority >= 7) return "bg-orange-100 text-orange-800";
  if (priority >= 5) return "bg-yellow-100 text-yellow-800";
  return "bg-green-100 text-green-800";
};
