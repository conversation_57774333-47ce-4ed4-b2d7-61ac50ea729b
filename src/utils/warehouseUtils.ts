
import { Warehouse, WarehousePerformance, WarehouseMetrics } from "@/types/warehouse";

export const mockWarehouses: Warehouse[] = [
  {
    id: "WH001",
    name: "Atlanta Distribution Center",
    location: "Atlanta, GA",
    type: "Distribution",
    status: "Active",
    capacity: 50000,
    utilization: 78,
    employees: 120,
    zones: ["Receiving", "Storage", "Picking", "Shipping"],
    region: "Southeast",
    manager: "<PERSON> Rodriguez",
    establishedDate: "2018-05-12",
    area: 500000,
    automationLevel: "Semi-Automated",
    temperatureControlled: false
  },
  {
    id: "WH002",
    name: "Los Angeles Fulfillment",
    location: "Los Angeles, CA",
    type: "Fulfillment",
    status: "Active",
    capacity: 75000,
    utilization: 85,
    employees: 180,
    zones: ["Receiving", "Storage", "Picking", "Packing", "Shipping"],
    region: "West Coast",
    manager: "David Chen",
    establishedDate: "2020-02-28",
    area: 650000,
    automationLevel: "Fully-Automated",
    temperatureControlled: true
  },
  {
    id: "WH003",
    name: "Chicago Cross-Dock",
    location: "Chicago, IL",
    type: "Cross-Dock",
    status: "Active",
    capacity: 30000,
    utilization: 92,
    employees: 80,
    zones: ["Inbound", "Sorting", "Outbound"],
    region: "Midwest",
    manager: "<PERSON> Johnson",
    establishedDate: "2019-09-15",
    area: 300000,
    automationLevel: "Semi-Automated",
    temperatureControlled: false
  },
  {
    id: "WH004",
    name: "Miami Cold Storage",
    location: "Miami, FL",
    type: "Storage",
    status: "Maintenance",
    capacity: 25000,
    utilization: 0,
    employees: 60,
    zones: ["Frozen", "Refrigerated", "Dry"],
    region: "Southeast",
    manager: "Carlos Martinez",
    establishedDate: "2017-11-03",
    area: 200000,
    automationLevel: "Manual",
    temperatureControlled: true
  }
];

export const mockWarehousePerformance: WarehousePerformance[] = [
  {
    warehouseId: "WH001",
    efficiency: 89,
    accuracy: 97,
    throughput: 1200,
    orderFulfillment: 94,
    inventoryTurnover: 8.5,
    pickingSpeed: 85,
    errorRate: 1.2
  },
  {
    warehouseId: "WH002",
    efficiency: 94,
    accuracy: 99,
    throughput: 1800,
    orderFulfillment: 98,
    inventoryTurnover: 12.3,
    pickingSpeed: 120,
    errorRate: 0.5
  },
  {
    warehouseId: "WH003",
    efficiency: 91,
    accuracy: 96,
    throughput: 800,
    orderFulfillment: 92,
    inventoryTurnover: 15.2,
    pickingSpeed: 95,
    errorRate: 1.8
  },
  {
    warehouseId: "WH004",
    efficiency: 0,
    accuracy: 0,
    throughput: 0,
    orderFulfillment: 0,
    inventoryTurnover: 0,
    pickingSpeed: 0,
    errorRate: 0
  }
];

export const calculateWarehouseMetrics = (warehouses: Warehouse[]): WarehouseMetrics => {
  const totalWarehouses = warehouses.length;
  const activeWarehouses = warehouses.filter(w => w.status === "Active").length;
  const totalCapacity = warehouses.reduce((sum, warehouse) => sum + warehouse.capacity, 0);
  const averageUtilization = warehouses.reduce((sum, warehouse) => sum + warehouse.utilization, 0) / warehouses.length;
  const totalEmployees = warehouses.reduce((sum, warehouse) => sum + warehouse.employees, 0);
  const maintenanceWarehouses = warehouses.filter(w => w.status === "Maintenance").length;

  return {
    totalWarehouses,
    activeWarehouses,
    totalCapacity,
    averageUtilization,
    totalEmployees,
    maintenanceWarehouses
  };
};

export const getStatusColor = (status: string) => {
  switch (status) {
    case "Active": return "bg-green-100 text-green-800";
    case "Inactive": return "bg-gray-100 text-gray-800";
    case "Maintenance": return "bg-yellow-100 text-yellow-800";
    case "Planned": return "bg-blue-100 text-blue-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

export const getAutomationColor = (level: string) => {
  switch (level) {
    case "Fully-Automated": return "bg-green-100 text-green-800";
    case "Semi-Automated": return "bg-blue-100 text-blue-800";
    case "Manual": return "bg-gray-100 text-gray-800";
    default: return "bg-gray-100 text-gray-800";
  }
};
